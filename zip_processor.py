import os
import tempfile
import zipfile
import logging
import requests
import shutil
import time
from urllib.parse import urlparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from collections import defaultdict

logger = logging.getLogger(__name__)

def create_robust_session():
    """Create a requests session with retry logic and better timeout handling."""
    session = requests.Session()

    # Configure retry strategy
    retry_strategy = Retry(
        total=3,  # Total number of retries
        backoff_factor=1,  # Wait time between retries
        status_forcelist=[429, 500, 502, 503, 504],  # HTTP status codes to retry
        allowed_methods=["HEAD", "GET", "OPTIONS"]  # HTTP methods to retry
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session

def is_zip_url(url):
    """Check if a URL points to a ZIP file."""
    try:
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        if path_parts and path_parts[-1]:
            filename = path_parts[-1].lower()
            return filename.endswith(".zip")
        
        # If no filename in path, try to check content type
        if '?' in url and '.zip' in url.lower():
            return True
    except Exception as e:
        logger.error(f"Error checking if URL is ZIP: {e}")
    return False


def optimize_for_nested_zips(url):
    """Analyze the ZIP file structure and return optimal processing parameters.
    
    This function performs a quick scan of the ZIP file to determine its complexity
    and returns optimized parameters for processing.
    
    Args:
        url: URL of the ZIP file
        
    Returns:
        dict: Processing parameters including max_workers, max_depth, etc.
    """
    try:
        # Download just the beginning of the file to check its structure
        # (Most ZIP files have directory at the end, but we can get basic info from the start)
        headers = {'Range': 'bytes=0-8192'}
        response = requests.get(url, headers=headers, timeout=10)
        
        # Get content length if available
        content_length = int(response.headers.get('Content-Length', 0))
        if content_length == 0 and 'Content-Range' in response.headers:
            # Try to parse from Content-Range header (bytes 0-8192/total)
            range_header = response.headers.get('Content-Range', '')
            if '/' in range_header:
                try:
                    content_length = int(range_header.split('/')[-1])
                except (ValueError, IndexError):
                    pass
        
        # Estimate file size and complexity
        file_size_mb = content_length / (1024 * 1024) if content_length > 0 else 10  # Default to 10MB if unknown
        
        # Determine optimal parameters based on file size
        if file_size_mb > 100:  # Very large ZIP (>100MB)
            return {
                'max_workers': min(64, (os.cpu_count() or 4) * 4),  # More workers for large files
                'max_depth': 15,  # Allow deeper nesting for complex archives
                'chunk_size': 1024 * 1024,  # 1MB chunks for large files
                'estimated_size_mb': file_size_mb
            }
        elif file_size_mb > 10:  # Medium ZIP (10-100MB)
            return {
                'max_workers': min(32, (os.cpu_count() or 4) * 2),
                'max_depth': 12,
                'chunk_size': 512 * 1024,  # 512KB chunks
                'estimated_size_mb': file_size_mb
            }
        else:  # Small ZIP (<10MB)
            return {
                'max_workers': min(16, (os.cpu_count() or 4) * 2),
                'max_depth': 10,
                'chunk_size': 256 * 1024,  # 256KB chunks
                'estimated_size_mb': file_size_mb
            }
            
    except Exception as e:
        logger.warning(f"Error optimizing ZIP parameters: {e}, using defaults")
        # Default parameters
        return {
            'max_workers': (os.cpu_count() or 4) * 2,
            'max_depth': 10,
            'chunk_size': 512 * 1024,
            'estimated_size_mb': 10
        }

def process_zip_from_url(url, max_workers=None, detect_zip_trap=True):
    """Process a ZIP file from a URL and return a detailed summary.
    
    Uses parallel processing for faster handling of nested ZIP files.
    Detects ZIP traps (archives containing only nested ZIP files).
    
    Args:
        url: The URL of the ZIP file
        max_workers: Maximum number of worker threads (default: auto-detect based on CPU)
        detect_zip_trap: If True, detects and exits early if the ZIP only contains other ZIP files
        
    Returns:
        dict: A dictionary containing the ZIP analysis results
    """
    start_time = time.time()
    
    # Determine optimal number of workers based on CPU cores
    if max_workers is None:
        max_workers = min(32, (os.cpu_count() or 4) * 2)  # Default to 2x CPU cores, max 32
    
    try:
        # Get optimal processing parameters
        processing_params = optimize_for_nested_zips(url)
        estimated_size = processing_params.get('estimated_size_mb', 10)

        # Extract filename from URL
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        if path_parts and path_parts[-1]:
            filename = path_parts[-1]
        else:
            filename = "archive.zip"

        # Download the ZIP file with robust session and progressive timeouts
        logger.info(f"Downloading ZIP file: {filename} (estimated size: {estimated_size:.1f} MB)")

        # Create robust session with retry logic
        session = create_robust_session()

        # Progressive timeout based on estimated file size
        if estimated_size > 100:
            timeout = (30, 300)  # (connect_timeout, read_timeout) for large files
        elif estimated_size > 10:
            timeout = (15, 180)  # Medium files
        else:
            timeout = (10, 120)   # Small files

        # Download with streaming for large files
        if estimated_size > 50:  # Stream large files
            logger.info(f"Streaming large file download with timeout {timeout}")
            response = session.get(url, timeout=timeout, stream=True)
            response.raise_for_status()

            # Create temporary file and write in chunks
            with tempfile.NamedTemporaryFile(delete=False) as tmp:
                tmp_path = tmp.name
                downloaded_size = 0
                chunk_size = processing_params.get('chunk_size', 1024 * 1024)

                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        tmp.write(chunk)
                        downloaded_size += len(chunk)

                        # Log progress for very large files
                        if downloaded_size % (10 * 1024 * 1024) == 0:  # Every 10MB
                            logger.info(f"Downloaded {downloaded_size / (1024*1024):.1f} MB...")
        else:
            # Regular download for smaller files
            logger.info(f"Downloading file with timeout {timeout}")
            response = session.get(url, timeout=timeout)
            response.raise_for_status()

            # Create a temporary file
            with tempfile.NamedTemporaryFile(delete=False) as tmp:
                tmp.write(response.content)
                tmp_path = tmp.name

        actual_size_mb = os.path.getsize(tmp_path) / (1024 * 1024)
        actual_size_kb = os.path.getsize(tmp_path) / 1024
        logger.info(f"Download completed: {actual_size_mb:.2f} MB")

        # Create a temporary directory for extraction
        base_extract_dir = tempfile.mkdtemp(prefix="zip_processor_")

        logger.info(f"Processing ZIP file: {filename} with {max_workers} parallel workers")
        
        # Shared data structures with thread-safe access
        from threading import Lock
        lock = Lock()
        all_files = []
        nested_zips = []
        extracted_content = []
        processing_steps = []
        
        # Add a processing step with thread safety
        def add_step(step):
            with lock:
                processing_steps.append(step)
        
        # Add a file with thread safety
        def add_file(file_data):
            with lock:
                all_files.append(file_data)
        
        # Add a nested zip with thread safety
        def add_nested_zip(zip_data):
            with lock:
                nested_zips.append(zip_data)
        
        # Add extracted content with thread safety
        def add_content(content_data):
            with lock:
                extracted_content.append(content_data)
        
        # Process a text file to extract its content
        def is_insurance_file(filename, content=None):
            """Determine if a file appears to be insurance-related based on name and optionally content.
            
            Args:
                filename: The filename to check
                content: Optional file content to analyze
                
            Returns:
                tuple: (is_insurance, insurance_type, confidence_score)
            """
            # Define common insurance file patterns
            insurance_filename_patterns = [
                r'(?i)policy',
                r'(?i)insurance',
                r'(?i)claim',
                r'(?i)coverage',
                r'(?i)certificate',
                r'(?i)declaration',
                r'(?i)endorsement',
                r'(?i)rider',
                r'(?i)premium',
                r'(?i)quote',
                r'(?i)renewal',
                r'(?i)cancellation',
                r'(?i)underwriting',
                r'(?i)benefit'
            ]
            
            # Define insurance content patterns if content is provided
            insurance_content_patterns = [
                r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)',
                r'(?i)certificate of insurance',
                r'(?i)coverage\s*(summary|details|limits)',
                r'(?i)premium\s*(amount|total|due|payment)',
                r'(?i)effective date',
                r'(?i)expiration date',
                r'(?i)policy period',
                r'(?i)named insured',
                r'(?i)policyholder',
                r'(?i)(covered|not covered|exclusion|excluded)',
                r'(?i)deductible',
                r'(?i)liability\s*(limit|coverage)',
                r'(?i)claim\s*(form|process|procedure)'
            ]
            
            # Check filename patterns
            filename_matches = []
            for pattern in insurance_filename_patterns:
                if re.search(pattern, filename):
                    filename_matches.append(pattern)
            
            # Initialize confidence score based on filename
            filename_score = min(70, len(filename_matches) * 20)
            
            # Determine insurance type based on filename
            insurance_type = "unknown"
            if re.search(r'(?i)policy|certificate', filename):
                insurance_type = "policy_document"
            elif re.search(r'(?i)claim', filename):
                insurance_type = "claim_document"
            elif re.search(r'(?i)endorsement|rider', filename):
                insurance_type = "policy_amendment"
            elif re.search(r'(?i)quote|proposal', filename):
                insurance_type = "insurance_quote"
            elif re.search(r'(?i)renewal', filename):
                insurance_type = "policy_renewal"
            elif re.search(r'(?i)cancellation', filename):
                insurance_type = "policy_cancellation"
            
            # Check content patterns if content is provided
            content_score = 0
            if content:
                content_matches = []
                for pattern in insurance_content_patterns:
                    if re.search(pattern, content):
                        content_matches.append(pattern)
                        
                # Content matching provides additional confidence
                content_score = min(80, len(content_matches) * 15)
                
                # Refine insurance type based on content if needed
                if insurance_type == "unknown" or content_score > filename_score:
                    if re.search(r'(?i)certificate of insurance', content):
                        insurance_type = "certificate_of_insurance"
                    elif re.search(r'(?i)claim\s*(form|report)', content):
                        insurance_type = "claim_document"
                    elif re.search(r'(?i)declaration\s*(page|statement)', content):
                        insurance_type = "declaration_page"
                    elif re.search(r'(?i)premium\s*quote', content):
                        insurance_type = "insurance_quote"
            
            # Determine final confidence score
            confidence_score = max(filename_score, content_score)
            if filename_score > 0 and content_score > 0:
                # Boost confidence if both filename and content match
                confidence_score = min(100, filename_score + content_score // 2)
            
            # Is this an insurance file?
            is_insurance = confidence_score >= 30
            
            return is_insurance, insurance_type, confidence_score

        def process_text_file(zip_ref, file_info, current_path):
            try:
                with zip_ref.open(file_info) as f:
                    content = f.read().decode('utf-8', errors='ignore')
                    # Truncate very large content
                    if len(content) > 10000:
                        content = content[:5000] + "\n\n[...content truncated...]\n\n" + content[-5000:]
                    
                    # Add insurance domain-specific analysis for text files
                    is_insurance, insurance_type, confidence_score = is_insurance_file(file_info.filename, content)
                    
                    # Add insurance metadata if applicable
                    if is_insurance:
                        insurance_metadata = f"\n\nINSURANCE FILE ANALYSIS:\n"
                        insurance_metadata += f"Type: {insurance_type.replace('_', ' ').title()}\n"
                        insurance_metadata += f"Confidence: {confidence_score}%\n\n"
                        
                        # Extract policy number if present
                        policy_match = re.search(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)', content)
                        if policy_match and policy_match.group(2):
                            insurance_metadata += f"Policy Number: {policy_match.group(2)}\n"
                        
                        # Add insurance metadata to the content
                        content = insurance_metadata + content
                    
                    add_content({
                        "path": current_path,
                        "content": content,
                        "is_insurance_file": is_insurance,
                        "insurance_type": insurance_type if is_insurance else None,
                        "confidence_score": confidence_score if is_insurance else 0
                    })
            except Exception as e:
                add_step(f"Error extracting text from {current_path}: {str(e)}")
        
        # Process a single ZIP file
        def process_zip_file(zip_path, prefix="", depth=0, max_depth=10, detect_zip_trap=detect_zip_trap):
            if depth > max_depth:
                add_step(f"Maximum recursion depth ({max_depth}) reached at {prefix or filename}")
                return {"error": "Maximum recursion depth reached"}
            
            add_step(f"Processing ZIP at depth {depth}: {prefix or filename}")
            
            try:
                # Safely open the ZIP file
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    file_list = zip_ref.infolist()
                    add_step(f"Found {len(file_list)} files in {prefix or filename}")
                    
                    # Check if this is a ZIP trap (only contains other ZIP files)
                    if detect_zip_trap and depth == 0:  # Only check at root level
                        non_dir_files = [f for f in file_list if not f.is_dir()]
                        zip_files = [f for f in non_dir_files if f.filename.lower().endswith(".zip")]
                        
                        # If all files are ZIP files, it might be a trap
                        if len(zip_files) > 0 and len(zip_files) == len(non_dir_files):
                            add_step("⚠️ ZIP trap detected: Archive contains only nested ZIP files")
                            
                            # Return early with a warning
                            zip_names = [f.filename for f in zip_files]
                            return {
                                "is_zip_trap": True,
                                "zip_count": len(zip_files),
                                "zip_files": zip_names,
                                "depth": depth,
                                "path": prefix or filename
                            }
                    
                    # Extract nested ZIP files for parallel processing
                    nested_zip_tasks = []
                    extract_dirs = []
                    
                    # First pass: process regular files and identify nested ZIPs
                    for file_info in file_list:
                        if file_info.is_dir():
                            continue
                            
                        # Build the full path
                        current_path = f"{prefix}/{file_info.filename}" if prefix else file_info.filename
                        
                        # Record this file
                        file_data = {
                            "name": file_info.filename,
                            "path": current_path,
                            "size": file_info.file_size,
                            "depth": depth
                        }
                        add_file(file_data)
                        
                        # Check if it's a text file we can extract
                        if file_info.filename.lower().endswith((".txt", ".md", ".csv", ".json", ".html", ".xml")):
                            process_text_file(zip_ref, file_info, current_path)
                        
                        # Prepare nested ZIP files for parallel processing
                        if file_info.filename.lower().endswith(".zip"):
                            add_nested_zip({
                                "path": current_path,
                                "depth": depth + 1
                            })
                            add_step(f"Found nested ZIP: {current_path}")
                            
                            # Extract the nested ZIP to a temporary directory
                            extract_dir = tempfile.mkdtemp(dir=base_extract_dir)
                            extract_dirs.append(extract_dir)
                            nested_zip_path = os.path.join(extract_dir, file_info.filename)
                            zip_ref.extract(file_info, extract_dir)
                            
                            # Add this ZIP to the tasks for parallel processing
                            nested_zip_tasks.append((nested_zip_path, current_path, depth + 1))
                    
                    # Process nested ZIPs in parallel if there are any
                    if nested_zip_tasks:
                        # Use a thread pool to process nested ZIPs in parallel
                        with ThreadPoolExecutor(max_workers=min(max_workers, len(nested_zip_tasks) + 1)) as executor:
                            # Submit all nested ZIP processing tasks
                            future_to_zip = {executor.submit(process_zip_file, zip_path, prefix, depth, max_depth): 
                                            (zip_path, prefix) for zip_path, prefix, depth in nested_zip_tasks}
                            
                            # Process results as they complete
                            for future in as_completed(future_to_zip):
                                zip_path, prefix = future_to_zip[future]
                                try:
                                    result = future.result()
                                    if result.get("error"):
                                        add_step(f"Error in nested ZIP {prefix}: {result['error']}")
                                except Exception as exc:
                                    add_step(f"Exception processing {prefix}: {exc}")
                
                return {
                    "file_count": len(file_list),
                    "depth": depth,
                    "path": prefix or filename,
                    "nested_count": len(nested_zip_tasks)
                }
                
            except zipfile.BadZipFile as e:
                add_step(f"Bad ZIP file at {prefix or filename}: {str(e)}")
                return {"error": f"Bad ZIP file: {str(e)}"}
            except Exception as e:
                add_step(f"Error processing ZIP at {prefix or filename}: {str(e)}")
                return {"error": str(e)}
        
        # Start recursive processing with the main ZIP file
        result = process_zip_file(tmp_path)
        
        # Clean up temporary files
        try:
            os.unlink(tmp_path)
            shutil.rmtree(base_extract_dir, ignore_errors=True)
        except Exception as e:
            add_step(f"Error cleaning up temporary files: {str(e)}")
            
        # Check if we detected a ZIP trap
        if result.get("is_zip_trap", False):
            # Generate a more user-friendly summary for nested ZIP files
            zip_count = result.get("zip_count", 0)
            zip_files = result.get("zip_files", [])

            # Create an engaging and informative message
            nested_summary = f"🗂️  ZIP Archive Analysis: {filename}\n"
            nested_summary += "=" * 50 + "\n\n"

            nested_summary += f"📦 ARCHIVE TYPE: Nested ZIP Collection\n\n"

            nested_summary += f"📊 OVERVIEW:\n"
            nested_summary += f"   • Archive Name: {filename}\n"
            nested_summary += f"   • Total Size: {actual_size_kb:.2f} KB ({actual_size_kb/1024:.2f} MB)\n"
            nested_summary += f"   • Nested ZIP Folders: {zip_count}\n"
            nested_summary += f"   • Structure Type: {'Multi-level nested archive' if zip_count > 1 else 'Single nested archive'}\n\n"

            # Detailed description based on count
            if zip_count == 1:
                nested_summary += f"🔍 STRUCTURE ANALYSIS:\n"
                nested_summary += f"   This ZIP folder contains 1 nested ZIP folder inside it.\n"
                nested_summary += f"   This is a common way to package and compress files for distribution.\n\n"
            elif zip_count <= 5:
                nested_summary += f"🔍 STRUCTURE ANALYSIS:\n"
                nested_summary += f"   This ZIP folder contains {zip_count} nested ZIP folders within it.\n"
                nested_summary += f"   This appears to be a collection of separate archives bundled together.\n\n"
            else:
                nested_summary += f"🔍 STRUCTURE ANALYSIS:\n"
                nested_summary += f"   This ZIP folder contains {zip_count} nested ZIP folders within it.\n"
                nested_summary += f"   This is a complex multi-level archive structure, possibly containing\n"
                nested_summary += f"   organized collections of files or a backup archive system.\n\n"

            # List the nested ZIP files with better formatting
            if zip_files:
                nested_summary += f"📁 NESTED ZIP FOLDERS:\n"
                for i, zip_name in enumerate(zip_files[:15], 1):  # Show first 15
                    nested_summary += f"   {i:2d}. 📦 {zip_name}\n"
                if len(zip_files) > 15:
                    nested_summary += f"   ... and {len(zip_files) - 15} more nested ZIP folders\n"
                nested_summary += "\n"

            # Add helpful guidance
            nested_summary += f"💡 WHAT THIS MEANS:\n"
            nested_summary += f"   • This archive contains other ZIP folders that need to be extracted separately\n"
            nested_summary += f"   • Each nested ZIP folder may contain different types of files or data\n"
            nested_summary += f"   • To access the actual files, you would need to:\n"
            nested_summary += f"     1. Extract this main ZIP folder\n"
            nested_summary += f"     2. Then extract each of the {zip_count} nested ZIP folders individually\n\n"

            nested_summary += f"🎯 RECOMMENDATION:\n"
            if zip_count <= 3:
                nested_summary += f"   This appears to be a manageable nested structure. You can extract\n"
                nested_summary += f"   the main archive and then process each nested ZIP folder as needed.\n"
            else:
                nested_summary += f"   This is a complex nested structure with {zip_count} ZIP folders.\n"
                nested_summary += f"   Consider using automated extraction tools or scripts to handle\n"
                nested_summary += f"   the multiple levels of compression efficiently.\n"

            return {
                "success": True,
                "is_zip_trap": True,
                "summary": nested_summary,
                "filename": filename,
                "size_kb": actual_size_kb,
                "total_files": zip_count,
                "total_nested_zips": zip_count,
                "processing_steps": processing_steps,
                "zip_files": zip_files
            }
        
        # Generate file types statistics
        file_types = {}
        for file in all_files:
            ext = os.path.splitext(file['name'].lower())[1]
            if ext:
                file_types[ext] = file_types.get(ext, 0) + 1
            else:
                file_types['no_extension'] = file_types.get('no_extension', 0) + 1
        
        # Sort files by path for better readability
        all_files.sort(key=lambda x: x['path'])
        
        # Build a detailed and user-friendly summary
        processing_time = time.time() - start_time
        total_size_kb = sum(f['size'] for f in all_files) / 1024

        # Create an engaging header
        summary = f"📁 ZIP Archive Analysis: {filename}\n"
        summary += "=" * 50 + "\n\n"

        # Overview section with emojis and clear formatting
        summary += "📊 OVERVIEW:\n"
        summary += f"   • Total Files Found: {len(all_files):,}\n"
        summary += f"   • Archive Size: {total_size_kb:.2f} KB ({total_size_kb/1024:.2f} MB)\n"
        summary += f"   • Processing Time: {processing_time:.2f} seconds\n"

        # Nested ZIP information with detailed description
        if nested_zips:
            summary += f"   • Nested Archives: {len(nested_zips)} ZIP folder{'s' if len(nested_zips) != 1 else ''} found\n"
            summary += f"\n🗂️  NESTED STRUCTURE:\n"
            summary += f"   This ZIP folder contains {len(nested_zips)} nested ZIP folder{'s' if len(nested_zips) != 1 else ''} within it.\n"

            # Group nested zips by depth for better understanding
            depth_groups = {}
            for zip_info in nested_zips:
                depth = zip_info['depth']
                if depth not in depth_groups:
                    depth_groups[depth] = []
                depth_groups[depth].append(zip_info)

            for depth in sorted(depth_groups.keys()):
                zips_at_depth = depth_groups[depth]
                if depth == 1:
                    summary += f"   📦 Level {depth} (Direct nested folders): {len(zips_at_depth)} ZIP{'s' if len(zips_at_depth) != 1 else ''}\n"
                else:
                    summary += f"   📦 Level {depth} (Deeply nested folders): {len(zips_at_depth)} ZIP{'s' if len(zips_at_depth) != 1 else ''}\n"

                for zip_info in sorted(zips_at_depth, key=lambda x: x['path'])[:5]:  # Show first 5 per level
                    summary += f"      - {zip_info['path']}\n"
                if len(zips_at_depth) > 5:
                    summary += f"      ... and {len(zips_at_depth) - 5} more at this level\n"
        else:
            summary += f"   • Nested Archives: None (simple archive)\n"

        # File types section with better formatting
        if file_types:
            summary += f"\n📋 FILE TYPES BREAKDOWN:\n"
            total_files = sum(file_types.values())
            for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_files) * 100
                ext_display = ext if ext != 'no_extension' else '(no extension)'
                summary += f"   • {ext_display}: {count:,} file{'s' if count != 1 else ''} ({percentage:.1f}%)\n"

        # Enhanced file listing with better organization
        summary += f"\n📄 FILE CONTENTS:\n"

        if len(all_files) == 0:
            summary += "   No files found in this archive.\n"
        elif len(all_files) <= 20:
            # Show all files for small archives
            summary += f"   Complete file listing ({len(all_files)} files):\n"
            for file in all_files:
                size_display = f"{file['size']/1024:.2f} KB" if file['size'] > 1024 else f"{file['size']} bytes"
                summary += f"   📄 {file['path']} ({size_display})\n"
        else:
            # Show organized view for larger archives
            summary += f"   Sample files (showing 15 of {len(all_files):,} total):\n"

            # Group files by directory for better organization
            dirs = {}
            for file in all_files:
                dir_path = '/'.join(file['path'].split('/')[:-1]) if '/' in file['path'] else 'root'
                if dir_path not in dirs:
                    dirs[dir_path] = []
                dirs[dir_path].append(file)

            shown_files = 0
            for dir_path in sorted(dirs.keys())[:5]:  # Show first 5 directories
                files_in_dir = dirs[dir_path]
                if dir_path == 'root':
                    summary += f"   📁 Root directory ({len(files_in_dir)} files):\n"
                else:
                    summary += f"   📁 {dir_path}/ ({len(files_in_dir)} files):\n"

                for file in files_in_dir[:3]:  # Show first 3 files per directory
                    if shown_files >= 15:
                        break
                    size_display = f"{file['size']/1024:.2f} KB" if file['size'] > 1024 else f"{file['size']} bytes"
                    summary += f"      📄 {file['name']} ({size_display})\n"
                    shown_files += 1

                if len(files_in_dir) > 3:
                    summary += f"      ... and {len(files_in_dir) - 3} more files in this directory\n"

                if shown_files >= 15:
                    break

            remaining_files = len(all_files) - shown_files
            if remaining_files > 0:
                summary += f"   ... and {remaining_files:,} more files in other directories\n"

        # Enhanced content preview section
        if extracted_content:
            summary += f"\n📖 TEXT CONTENT PREVIEW:\n"
            summary += f"   Found readable text in {len(extracted_content)} file{'s' if len(extracted_content) != 1 else ''}:\n\n"

            for i, content in enumerate(extracted_content[:3]):  # Show first 3 text files
                summary += f"   📄 {content['path']}:\n"
                preview_text = content['content'][:500].strip()
                if len(content['content']) > 500:
                    preview_text += "..."
                # Indent the content for better readability
                indented_content = '\n'.join(f"      {line}" for line in preview_text.split('\n'))
                summary += f"{indented_content}\n\n"

            if len(extracted_content) > 3:
                summary += f"   📄 ... and {len(extracted_content) - 3} more text files with readable content\n"

        # Add helpful summary footer
        summary += f"\n💡 SUMMARY:\n"
        if nested_zips:
            summary += f"   This is a complex archive containing {len(nested_zips)} nested ZIP folder{'s' if len(nested_zips) != 1 else ''}.\n"
            summary += f"   To fully explore the contents, you would need to extract the nested archives individually.\n"
        else:
            summary += f"   This is a standard archive with {len(all_files):,} file{'s' if len(all_files) != 1 else ''}.\n"

        if file_types:
            most_common = max(file_types.items(), key=lambda x: x[1])
            ext_name = most_common[0] if most_common[0] != 'no_extension' else 'files without extensions'
            summary += f"   Most common file type: {ext_name} ({most_common[1]} files)\n"
        
        logger.info(f"ZIP processing completed in {processing_time:.2f} seconds. Found {len(all_files)} files and {len(nested_zips)} nested ZIPs")
        
        # Return the analysis results
        return {
            "success": True,
            "summary": summary,
            "filename": filename,
            "size_kb": actual_size_kb,
            "total_files": len(all_files),
            "total_nested_zips": len(nested_zips),
            "processing_time": processing_time,
            "file_types": file_types,
            "nested_zips": [nz['path'] for nz in nested_zips],
            "processing_steps": processing_steps,
            "extracted_content": extracted_content[:10],  # Limit to first 10 text files
            "files": all_files[:100]  # Limit to first 100 files
        }
        
    except requests.exceptions.Timeout as e:
        logger.error(f"Timeout downloading ZIP file: {e}")
        return {
            "success": False,
            "error": "Download timeout",
            "user_friendly_error": "The ZIP file download timed out. This usually happens with very large files or slow network connections.",
            "summary": f"⚠️  Download Timeout: {filename}\n\n" +
                      f"The ZIP file could not be downloaded due to a timeout.\n" +
                      f"This typically occurs when:\n" +
                      f"• The file is very large (>100MB)\n" +
                      f"• The network connection is slow\n" +
                      f"• The server is experiencing high load\n\n" +
                      f"💡 SUGGESTIONS:\n" +
                      f"• Try again in a few minutes\n" +
                      f"• Check if the file is accessible directly\n" +
                      f"• Consider using a smaller ZIP file if possible\n" +
                      f"• Verify the URL is correct and the file exists"
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Connection error downloading ZIP file: {e}")
        return {
            "success": False,
            "error": "Connection error",
            "user_friendly_error": "Could not connect to the server to download the ZIP file.",
            "summary": f"🔌 Connection Error: {filename}\n\n" +
                      f"Unable to establish a connection to download the ZIP file.\n" +
                      f"This could be due to:\n" +
                      f"• Network connectivity issues\n" +
                      f"• Server being temporarily unavailable\n" +
                      f"• Incorrect URL or domain name\n" +
                      f"• Firewall or proxy blocking the connection\n\n" +
                      f"💡 SUGGESTIONS:\n" +
                      f"• Check your internet connection\n" +
                      f"• Verify the URL is correct\n" +
                      f"• Try accessing the URL in a web browser\n" +
                      f"• Contact the file provider if the issue persists"
        }
    except requests.exceptions.HTTPError as e:
        status_code = e.response.status_code if e.response else "Unknown"
        logger.error(f"HTTP error downloading ZIP file: {e}")

        if status_code == 404:
            error_msg = "The ZIP file was not found at the specified URL."
            suggestions = "• Verify the URL is correct\n• Check if the file has been moved or deleted\n• Contact the file provider"
        elif status_code == 403:
            error_msg = "Access to the ZIP file is forbidden."
            suggestions = "• Check if you have permission to access this file\n• Verify authentication credentials if required\n• Contact the file provider"
        elif status_code == 500:
            error_msg = "The server encountered an error while serving the ZIP file."
            suggestions = "• Try again in a few minutes\n• Contact the file provider if the issue persists"
        else:
            error_msg = f"The server returned an error (HTTP {status_code})."
            suggestions = "• Try again in a few minutes\n• Check if the URL is correct\n• Contact the file provider"

        return {
            "success": False,
            "error": f"HTTP {status_code}",
            "user_friendly_error": error_msg,
            "summary": f"🚫 Access Error: {filename}\n\n" +
                      f"{error_msg}\n\n" +
                      f"💡 SUGGESTIONS:\n" +
                      f"{suggestions}"
        }
    except zipfile.BadZipFile as e:
        logger.error(f"Invalid ZIP file: {e}")
        return {
            "success": False,
            "error": "Invalid ZIP file",
            "user_friendly_error": "The downloaded file is not a valid ZIP archive.",
            "summary": f"📦 Invalid ZIP File: {filename}\n\n" +
                      f"The file appears to be corrupted or is not a valid ZIP archive.\n" +
                      f"This could be due to:\n" +
                      f"• Incomplete download\n" +
                      f"• File corruption during transfer\n" +
                      f"• The file is not actually a ZIP archive\n" +
                      f"• Password-protected ZIP file\n\n" +
                      f"💡 SUGGESTIONS:\n" +
                      f"• Try downloading the file again\n" +
                      f"• Verify the file is a ZIP archive\n" +
                      f"• Check if the file requires a password\n" +
                      f"• Contact the file provider if the issue persists"
        }
    except Exception as e:
        logger.error(f"Unexpected error processing ZIP file: {e}")
        return {
            "success": False,
            "error": str(e),
            "user_friendly_error": "An unexpected error occurred while processing the ZIP file.",
            "summary": f"⚠️  Processing Error: {filename}\n\n" +
                      f"An unexpected error occurred while processing the ZIP file.\n" +
                      f"Error details: {str(e)}\n\n" +
                      f"💡 SUGGESTIONS:\n" +
                      f"• Try again in a few minutes\n" +
                      f"• Check if the file is accessible and valid\n" +
                      f"• Contact support if the issue persists\n" +
                      f"• Provide the error details above for troubleshooting"
        }