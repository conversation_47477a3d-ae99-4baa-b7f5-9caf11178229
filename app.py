from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
import requests
import json
import fitz
import faiss
import numpy as np
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import ThreadPoolExecutor
import re
from bs4 import BeautifulSoup
from docx import Document
import tempfile
import email
from email import policy
import extract_msg
from openai import OpenAI
import hashlib
import pickle
from functools import lru_cache
import logging
import base64
import io
from pptx import Presentation

# Import our custom processors
from zip_processor import is_zip_url, process_zip_from_url
from data_processor import is_excel_or_csv_url, process_excel_csv_from_url, answer_question_from_data

# Additional imports for enhanced processing
import pdfplumber
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
# Remove sentence_transformers dependency for faster processing

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import tabula with proper error handling after logger is configured
try:
    import tabula
    TABULA_AVAILABLE = True
    logger.info("Tabula-py loaded successfully for enhanced PDF table extraction")
except ImportError as e:
    TABULA_AVAILABLE = False
    logger.warning(f"Tabula-py not available: {e}. PDF table extraction will use pdfplumber only.")

# Import OCR libraries for image text extraction
try:
    import pytesseract
    import cv2
    from PIL import Image as PILImage
    OCR_AVAILABLE = True
    logger.info("OCR libraries loaded successfully for image text extraction")

    # Try to configure tesseract path (common Windows installation)
    try:
        # Test if tesseract is available
        pytesseract.get_tesseract_version()
    except pytesseract.TesseractNotFoundError:
        # Try common Windows installation paths
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', ''))
        ]

        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                logger.info(f"Tesseract found at: {path}")
                break
        else:
            logger.warning("Tesseract executable not found. OCR functionality may not work.")

except ImportError as e:
    OCR_AVAILABLE = False
    logger.warning(f"OCR libraries not available: {e}. Image text extraction will be skipped.")

load_dotenv()
app = Flask(__name__)
CORS(app)

# Request/Response logging middleware
@app.before_request
def log_request_info():
    logger.info('=== INCOMING REQUEST ===')
    logger.info(f'Method: {request.method}')
    logger.info(f'URL: {request.url}')
    logger.info(f'Remote Address: {request.remote_addr}')
    logger.info(f'Headers: {dict(request.headers)}')
    logger.info(f'Content Type: {request.content_type}')
    logger.info(f'Content Length: {request.content_length}')
    
    # Log request body for POST requests
    if request.method == 'POST':
        try:
            if request.content_type and 'application/json' in request.content_type:
                logger.info(f'Request Body (JSON): {request.get_data(as_text=True)}')
            else:
                logger.info(f'Request Body (Raw): {request.get_data()}')
        except Exception as e:
            logger.error(f'Error reading request body: {e}')
    logger.info('========================')

@app.after_request
def log_response_info(response):
    logger.info('=== OUTGOING RESPONSE ===')
    logger.info(f'Status Code: {response.status_code}')
    logger.info(f'Status: {response.status}')
    logger.info(f'Response Headers: {dict(response.headers)}')
    
    # Log response body for non-200 responses or if it's small
    try:
        response_data = response.get_data(as_text=True)
        if response.status_code != 200 or len(response_data) < 1000:
            logger.info(f'Response Body: {response_data}')
        else:
            logger.info(f'Response Body Length: {len(response_data)} characters')
    except Exception as e:
        logger.error(f'Error reading response body: {e}')
    
    logger.info('=========================')
    return response

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

CACHE_DIR = "cache"
os.makedirs(CACHE_DIR, exist_ok=True)

# Cache item wrapper for access tracking
class CacheItem:
    """Wrapper for cached data with access tracking"""
    def __init__(self, data):
        self.data = data
        self._access_count = 1

    def access(self):
        """Increment access count and return data"""
        self._access_count += 1
        return self.data

# Tiered in-memory cache for frequently accessed data
memory_cache = {}  # Primary cache for most recent/frequently accessed items
frequent_cache = {}  # Secondary cache for frequently accessed items
MEMORY_CACHE_SIZE = 250  # Increased from 100 to improve hit rate
FREQUENT_CACHE_SIZE = 500  # Secondary cache for frequently accessed items

# Pre-compiled regex patterns for better performance
REGEX_PATTERNS = {
    'page_headers': re.compile(r"Page \d+ of \d+"),
    'whitespace': re.compile(r"\s{2,}"),
    'broken_words': re.compile(r"(\w+)-\s*\n\s*(\w+)"),
    'currency': re.compile(r"\$\s+(\d)"),
    'percentage': re.compile(r"(\d)\s+%"),
    'newlines': re.compile(r"\n{3,}"),
    'definitions': re.compile(r'\b([A-Z][\w\s]+)\s+means\s+([^\.]+\.)', re.MULTILINE),
    'first_sentence': re.compile(r'[.!?]\s'),
    'insurance_keywords': re.compile(r'\b(?:claim|premium|coverage|deductible|exclusion|benefit|policy|insured|limit|condition|amount|liability|copay|coinsurance|network|provider|reimbursement|payment|cost|fee|charge|expense|maximum|minimum|percentage|dollar|annual|monthly|eligible|eligibility|waiting|period|effective|date|termination|renewal|grace|notification|approval|document|required|submission|proof|evidence|terms|clause|diagnosis|treatment|procedure|physician|hospital|prescription|medication|emergency|preventive|specialist|definition|scope|coverage|exclusion|exception|provision|endorsement|schedule|attachment|addendum)\b', re.IGNORECASE),
    # Enhanced patterns for tabular data detection
    'table_headers': re.compile(r'\b(?:name|amount|date|policy|number|premium|coverage|limit|deductible|benefit|claim|status|type|description|value|total|subtotal|percentage|rate|fee|cost|price|sum|balance|due|paid|outstanding|remaining)\b', re.IGNORECASE),
    'table_separators': re.compile(r'[\|\t]{2,}|[-=]{3,}|\s{4,}'),
    'numeric_data': re.compile(r'\b\d{1,3}(?:,\d{3})*(?:\.\d{2})?\b|\$\d+(?:,\d{3})*(?:\.\d{2})?|\d+%'),
    'structured_lists': re.compile(r'^\s*(?:\d+\.|\w\)|\•|\*|\-)\s+', re.MULTILINE),
    'section_headers': re.compile(r'^[A-Z][A-Z\s]{2,}:?\s*$', re.MULTILINE),
    'key_value_pairs': re.compile(r'([A-Za-z\s]+):\s*([^\n]+)', re.MULTILINE)
}


@lru_cache(maxsize=1000)
def clean_text(text):
    """Enhanced text cleaning with better preprocessing for accuracy."""
    if not text or not text.strip():
        return ""

    # Remove page headers/footers
    text = REGEX_PATTERNS['page_headers'].sub("", text)

    # Preserve important punctuation but clean excessive whitespace
    text = REGEX_PATTERNS['whitespace'].sub(" ", text)

    # Fix broken words across lines (common in PDFs)
    text = REGEX_PATTERNS['broken_words'].sub(r"\1\2", text)

    # Preserve currency and percentage formatting
    text = REGEX_PATTERNS['currency'].sub(r"$\1", text)
    text = REGEX_PATTERNS['percentage'].sub(r"\1%", text)

    # Clean but preserve section markers
    text = REGEX_PATTERNS['newlines'].sub("\n\n", text)

    return text.strip()


def enhanced_text_preprocessing(text):
    """Advanced text preprocessing with tabular data preservation."""
    if not text or not text.strip():
        return ""

    # First apply basic cleaning
    text = clean_text(text)

    # Detect and preserve tabular structures
    table_indicators = []

    # Check for table headers
    if REGEX_PATTERNS['table_headers'].search(text):
        table_indicators.append("has_table_headers")

    # Check for table separators
    if REGEX_PATTERNS['table_separators'].search(text):
        table_indicators.append("has_table_structure")

    # Check for numeric data patterns
    numeric_matches = REGEX_PATTERNS['numeric_data'].findall(text)
    if len(numeric_matches) > 3:  # Multiple numeric values suggest tabular data
        table_indicators.append("has_numeric_data")

    # Check for structured lists
    if REGEX_PATTERNS['structured_lists'].search(text):
        table_indicators.append("has_structured_lists")

    # If tabular data detected, preserve structure better
    if table_indicators:
        # Preserve table-like spacing and alignment
        text = re.sub(r'\s{2,}', ' | ', text)  # Convert multiple spaces to pipe separators
        text = re.sub(r'\n\s*\n', '\n', text)  # Reduce excessive line breaks in tables

        # Add metadata about table structure
        text = f"[TABLE_DATA_DETECTED: {', '.join(table_indicators)}]\n{text}"

    # Enhance section header detection
    section_headers = REGEX_PATTERNS['section_headers'].findall(text)
    if section_headers:
        for header in section_headers:
            text = text.replace(header, f"\n\n=== {header.strip()} ===\n")

    # Preserve key-value pairs structure
    kv_pairs = REGEX_PATTERNS['key_value_pairs'].findall(text)
    if len(kv_pairs) > 2:  # Multiple key-value pairs
        text = f"[KEY_VALUE_PAIRS_DETECTED]\n{text}"

    return text.strip()


def extract_tabular_data_from_pdf(pdf_path):
    """Extract tabular data from PDF using multiple methods."""
    tabular_content = []

    try:
        # Method 1: Use pdfplumber for table extraction
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                # Extract tables
                tables = page.extract_tables()
                if tables:
                    for table_num, table in enumerate(tables, 1):
                        if table and len(table) > 1:  # Valid table with header and data
                            # Convert table to text format
                            table_text = f"Table {table_num} on Page {page_num}:\n"
                            for row in table:
                                if row and any(cell for cell in row if cell):  # Non-empty row
                                    clean_row = [str(cell).strip() if cell else "" for cell in row]
                                    table_text += " | ".join(clean_row) + "\n"
                            tabular_content.append(table_text)
    except Exception as e:
        logger.warning(f"pdfplumber table extraction failed: {e}")

    # Method 2: Use tabula for additional table detection (if available)
    if TABULA_AVAILABLE:
        try:
            tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True, silent=True)
            if tables:
                for i, df in enumerate(tables, 1):
                    if not df.empty and len(df) > 1:
                        table_text = f"Tabula Table {i}:\n"
                        table_text += df.to_string(index=False, na_rep='')
                        tabular_content.append(table_text)
        except Exception as e:
            logger.warning(f"tabula table extraction failed: {e}")
    else:
        logger.debug("Tabula not available, skipping additional table extraction")

    return tabular_content

@lru_cache(maxsize=1000)
def get_cache_key(url):
    return hashlib.md5(url.encode()).hexdigest()

def manage_memory_cache():
    """Implement tiered caching strategy for better performance
    
    This function manages two cache levels:
    1. memory_cache: Primary cache for most recent items
    2. frequent_cache: Secondary cache for frequently accessed items
    """
    # Manage primary cache
    if len(memory_cache) > MEMORY_CACHE_SIZE:
        # Move frequently accessed items to frequent_cache before removing
        access_counts = {}
        for key in list(memory_cache.keys()):
            cache_item = memory_cache[key]
            if isinstance(cache_item, CacheItem):
                access_counts[key] = cache_item._access_count
            elif hasattr(cache_item, '_access_count'):
                access_counts[key] = cache_item._access_count
            else:
                # Handle legacy cache items without wrapper
                access_counts[key] = 1
        
        # Find top 20% most accessed items
        if access_counts:
            top_items = sorted(access_counts.items(), key=lambda x: x[1], reverse=True)
            top_items = top_items[:int(len(access_counts) * 0.2)]
            
            # Move these items to frequent_cache
            for key, _ in top_items:
                if key in memory_cache and key not in frequent_cache:
                    frequent_cache[key] = memory_cache[key]
        
        # Remove oldest items from primary cache
        items_to_remove = len(memory_cache) - int(MEMORY_CACHE_SIZE * 0.8)
        for _ in range(items_to_remove):
            if memory_cache:  # Check if not empty
                memory_cache.pop(next(iter(memory_cache)))
    
    # Manage secondary cache
    if len(frequent_cache) > FREQUENT_CACHE_SIZE:
        # Remove least accessed items
        access_counts = {}
        for key, item in frequent_cache.items():
            if isinstance(item, CacheItem):
                access_counts[key] = item._access_count
            else:
                access_counts[key] = getattr(item, '_access_count', 0)
        sorted_items = sorted(access_counts.items(), key=lambda x: x[1])
        
        # Remove bottom 20%
        items_to_remove = int(len(frequent_cache) * 0.2)
        for key, _ in sorted_items[:items_to_remove]:
            frequent_cache.pop(key, None)

def save_cache(cache_key, data):
    # Wrap data in CacheItem for access tracking
    cache_item = CacheItem(data)

    # Save to memory cache first for faster access
    memory_cache[cache_key] = cache_item
    manage_memory_cache()

    # Also save to disk for persistence (save the raw data, not the wrapper)
    try:
        with open(os.path.join(CACHE_DIR, f"{cache_key}.pkl"), "wb") as f:
            pickle.dump(data, f)
    except Exception:
        pass  # Don't fail if disk cache fails

def load_cache(cache_key):
    """Enhanced cache loading with tiered strategy and access tracking
    
    Implements a tiered lookup strategy:
    1. Check primary memory cache
    2. Check secondary (frequent) memory cache
    3. Fall back to disk cache
    
    Also tracks access counts for better cache management.
    """
    # Check primary memory cache first (fastest)
    if cache_key in memory_cache:
        cache_item = memory_cache[cache_key]
        if isinstance(cache_item, CacheItem):
            return cache_item.access()
        else:
            # Handle legacy cache items - wrap them
            wrapped_item = CacheItem(cache_item)
            memory_cache[cache_key] = wrapped_item
            return wrapped_item.access()
    
    # Check secondary cache next (still fast)
    if cache_key in frequent_cache:
        cache_item = frequent_cache[cache_key]
        if isinstance(cache_item, CacheItem):
            data = cache_item.access()
            # Move back to primary cache if frequently accessed
            if cache_item._access_count > 3:
                memory_cache[cache_key] = cache_item
                manage_memory_cache()
            return data
        else:
            # Handle legacy cache items - wrap them
            wrapped_item = CacheItem(cache_item)
            frequent_cache[cache_key] = wrapped_item
            data = wrapped_item.access()
            # Move to primary cache
            memory_cache[cache_key] = wrapped_item
            manage_memory_cache()
            return data

    # Fall back to disk cache
    path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
    if os.path.exists(path):
        try:
            with open(path, "rb") as f:
                data = pickle.load(f)
                # Wrap in CacheItem for access tracking
                cache_item = CacheItem(data)
                # Store in memory cache for next time
                memory_cache[cache_key] = cache_item
                manage_memory_cache()
                return cache_item.access()
        except Exception as e:
            logger.warning(f"Cache loading error: {e}")
            # Don't fail if disk cache is corrupted
    return None


def is_image_file(filename, content_type=""):
    """Check if a file is an image file that needs LLM processing."""
    image_extensions = [".jpg", ".jpeg", ".png"]
    return (filename.lower().endswith(tuple(image_extensions)) or
            any(img_type in content_type.lower() for img_type in ["jpeg", "jpg", "png"]))


def is_powerpoint_file(filename, content_type=""):
    """Check if a file is a PowerPoint file that needs LLM processing."""
    return (filename.lower().endswith(".pptx") or
            "presentationml" in content_type.lower())


def process_image_with_llm(image_path, filename):
    """Process an image file using OpenAI's vision model with insurance domain-specific instructions."""
    try:
        # Read and encode the image
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
            base64_image = base64.b64encode(image_data).decode('utf-8')

        # Insurance-specific instructions for the vision model
        insurance_instructions = """
Analyze this insurance-related image and extract all information relevant for insurance purposes. Focus on:

1. Any policy information including policy numbers, coverage amounts, premiums, and dates
2. Insurance company names, logos, and contact information
3. Coverage details, limits, deductibles, and exclusions
4. Claim forms, declarations, or endorsements
5. Tables showing premium calculations, benefit schedules, or coverage limits
6. Signatures, dates, stamps, and other verification elements
7. Any warnings, notices, or important information that appears highlighted or boxed

Organize your response with clear headings for different types of information. If the image is not insurance-related, briefly describe the content instead.
"""

        # Use OpenAI's vision model to analyze the image with insurance focus
        response = client.chat.completions.create(
            model="gpt-4.1-mini",  # Vision-capable model
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": insurance_instructions
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1200,  # Increased token limit for more detailed extraction
            temperature=0.2    # Lower temperature for more precise extraction
        )

        extracted_text = response.choices[0].message.content
        
        # Process the response to highlight insurance-specific information
        processed_text = extracted_text
        
        # Add structured prefix based on filename
        prefix = "INSURANCE DOCUMENT IMAGE ANALYSIS:\n"
        prefix += f"Filename: {filename}\n"
        
        # Check if certain key insurance terms are present in the response
        insurance_terms = ["policy", "coverage", "premium", "claim", "insurance", 
                          "deductible", "benefits", "exclusion", "limit"]
                          
        detected_terms = [term for term in insurance_terms if term in extracted_text.lower()]
        if detected_terms:
            prefix += f"Insurance elements detected: {', '.join(detected_terms)}\n\n"
        else:
            prefix += "No specific insurance elements clearly detected\n\n"
            
        # Final formatted text with insurance-specific prefix
        final_text = prefix + processed_text
        
        return [clean_text(final_text)]

    except Exception as e:
        logger.error(f"Error processing image with LLM: {e}")
        return [f"Error processing image {filename}: {str(e)}"]


def extract_shapes_recursive(shape_container):
    """Extract text recursively from shapes and groups in PowerPoint slides with enhanced extraction."""
    texts = []

    for shape in shape_container.shapes:
        try:
            shape_content = []

            # Method 1: Direct text extraction
            if hasattr(shape, "text") and shape.text.strip():
                shape_content.append(shape.text.strip())

            # Method 2: Text frame extraction (more comprehensive)
            if hasattr(shape, "text_frame") and shape.text_frame:
                frame_text = []
                for paragraph in shape.text_frame.paragraphs:
                    para_parts = []
                    for run in paragraph.runs:
                        if run.text.strip():
                            # Preserve formatting information
                            text_part = run.text.strip()
                            if hasattr(run.font, 'bold') and run.font.bold:
                                text_part = f"**{text_part}**"
                            if hasattr(run.font, 'italic') and run.font.italic:
                                text_part = f"*{text_part}*"
                            para_parts.append(text_part)

                    if para_parts:
                        paragraph_text = " ".join(para_parts)
                        # Check for bullet points or numbering
                        if hasattr(paragraph, 'level') and paragraph.level > 0:
                            indent = "  " * paragraph.level
                            paragraph_text = f"{indent}• {paragraph_text}"
                        elif not paragraph_text.startswith(('•', '-', '*', '1.', '2.', '3.')):
                            paragraph_text = f"• {paragraph_text}"
                        frame_text.append(paragraph_text)

                if frame_text and not shape_content:  # Only add if we didn't get text from direct method
                    shape_content.extend(frame_text)

            # Handle table shapes with enhanced extraction
            if hasattr(shape, "has_table") and shape.has_table:
                try:
                    table_data = []
                    table = shape.table

                    # Extract table with proper structure
                    for row in table.rows:
                        row_data = []
                        for cell in row.cells:
                            cell_content = ""

                            # Try multiple extraction methods
                            if hasattr(cell, "text") and cell.text.strip():
                                cell_content = cell.text.strip()
                            elif hasattr(cell, "text_frame") and cell.text_frame:
                                cell_parts = []
                                for paragraph in cell.text_frame.paragraphs:
                                    para_parts = []
                                    for run in paragraph.runs:
                                        if run.text.strip():
                                            para_parts.append(run.text.strip())
                                    if para_parts:
                                        cell_parts.append(" ".join(para_parts))
                                cell_content = "\n".join(cell_parts) if cell_parts else ""

                            # Clean and format cell content
                            if cell_content:
                                cell_content = cell_content.replace('\n', ' ').replace('\r', ' ')
                                cell_content = ' '.join(cell_content.split())  # Normalize whitespace
                                row_data.append(cell_content)
                            else:
                                row_data.append("")

                        if any(cell.strip() for cell in row_data):  # Only add non-empty rows
                            table_data.append(row_data)

                    if table_data:
                        # Format table with headers and data distinction
                        table_text = ["[TABLE]"]
                        if len(table_data) > 0:
                            # First row as headers
                            headers = table_data[0]
                            table_text.append("Headers: " + " | ".join(headers))

                            # Remaining rows as data
                            if len(table_data) > 1:
                                table_text.append("Data:")
                                for row in table_data[1:]:
                                    table_text.append("  " + " | ".join(row))

                        shape_content.append("\n".join(table_text))

                except Exception as e:
                    logger.warning(f"Error extracting table content: {e}")
                    shape_content.append("[TABLE - extraction error]")

            # Handle group shapes (shapes within shapes)
            if hasattr(shape, "shape_type") and shape.shape_type == 6:  # GROUP shape type
                try:
                    group_texts = extract_shapes_recursive(shape)
                    if group_texts:
                        shape_content.extend(group_texts)
                except Exception as e:
                    logger.warning(f"Error extracting group shape content: {e}")

            # Handle charts with enhanced extraction
            if hasattr(shape, "has_chart") and shape.has_chart:
                try:
                    chart_info = ["[CHART]"]
                    chart = shape.chart

                    # Extract chart title
                    if hasattr(chart, "chart_title") and chart.chart_title:
                        if hasattr(chart.chart_title, "text_frame") and chart.chart_title.text_frame.text:
                            chart_info.append(f"Title: {chart.chart_title.text_frame.text.strip()}")

                    # Extract chart type information
                    if hasattr(chart, "chart_type"):
                        chart_info.append(f"Type: {str(chart.chart_type)}")

                    # Extract category labels
                    if hasattr(chart, "plots") and chart.plots:
                        for plot in chart.plots:
                            if hasattr(plot, "categories") and plot.categories:
                                categories = [str(cat) for cat in plot.categories if cat]
                                if categories:
                                    chart_info.append("Categories: " + ", ".join(categories[:10]))  # Limit to first 10

                    # Extract series information
                    if hasattr(chart, "series") and chart.series:
                        series_data = []
                        for series in chart.series:
                            series_info = []
                            if hasattr(series, "name") and series.name:
                                series_info.append(f"Name: {series.name}")

                            # Try to extract some data points
                            if hasattr(series, "values") and series.values:
                                try:
                                    values = [str(v) for v in series.values if v is not None][:5]  # First 5 values
                                    if values:
                                        series_info.append(f"Sample values: {', '.join(values)}")
                                except:
                                    pass

                            if series_info:
                                series_data.append(" | ".join(series_info))

                        if series_data:
                            chart_info.append("Series data:")
                            for series in series_data:
                                chart_info.append(f"  {series}")

                    shape_content.append("\n".join(chart_info))

                except Exception as e:
                    logger.warning(f"Error extracting chart content: {e}")
                    shape_content.append("[CHART - extraction error]")

            # Handle image shapes with OCR text extraction
            if hasattr(shape, 'shape_type'):
                # Check for image shape types (expanded list)
                image_shape_types = [13, 14, 15, 16, 17, 18, 19]  # Various image and media types
                if shape.shape_type in image_shape_types:
                    try:
                        # Try to extract text from image using OCR
                        ocr_text = extract_text_from_image_shape(shape, getattr(shape_container, 'slide_number', 0))
                        if ocr_text:
                            shape_content.append(f"[IMAGE_TEXT] {ocr_text}")
                            logger.debug(f"OCR extracted text from image: {ocr_text[:50]}...")
                    except Exception as e:
                        logger.debug(f"OCR extraction failed for image shape: {e}")

                # For slides with no text content, try OCR on any shape that might contain images
                elif not shape_content and OCR_AVAILABLE:
                    # Try OCR on shapes that might be images but not detected as such
                    try:
                        if (hasattr(shape, 'width') and hasattr(shape, 'height') and
                            shape.width > 50 and shape.height > 50 and
                            not hasattr(shape, 'text')):
                            # This might be an image or graphic with text
                            ocr_text = extract_text_from_image_shape(shape, getattr(shape_container, 'slide_number', 0))
                            if ocr_text:
                                shape_content.append(f"[IMAGE_TEXT] {ocr_text}")
                                logger.debug(f"OCR extracted text from potential image shape: {ocr_text[:50]}...")
                    except Exception as e:
                        logger.debug(f"OCR extraction failed for potential image shape: {e}")

            # Add all extracted content from this shape
            if shape_content:
                texts.extend(shape_content)

        except Exception as e:
            logger.warning(f"Error processing shape: {e}")
            continue

    return texts

def process_powerpoint_for_qa(pptx_path, filename):
    """Process a PowerPoint file by extracting text content for direct Q&A without LLM pre-processing."""
    try:
        # Extract text from PowerPoint
        prs = Presentation(pptx_path)
        slide_texts = []
        slide_structure = {}
        total_slides = len(prs.slides)
        
        logger.info(f"Processing PowerPoint with {total_slides} slides: {filename}")

        for i, slide in enumerate(prs.slides, 1):
            # Create structured slide information with enhanced metadata
            slide_info = {
                "number": i,
                "title": "",
                "content": [],
                "has_table": False,
                "has_chart": False,
                "has_image": False,
                "layout_name": "",
                "shape_count": len(slide.shapes),
                "content_types": []
            }

            # Get slide layout information
            try:
                if hasattr(slide, "slide_layout") and hasattr(slide.slide_layout, "name"):
                    slide_info["layout_name"] = slide.slide_layout.name
            except:
                pass

            # Extract slide title with multiple methods
            title_candidates = []

            # Method 1: Title placeholder
            for shape in slide.shapes:
                try:
                    if (hasattr(shape, "is_placeholder") and shape.is_placeholder and
                        hasattr(shape, "placeholder_format") and shape.placeholder_format.type == 1):
                        if hasattr(shape, "text") and shape.text.strip():
                            title_candidates.append(("placeholder", shape.text.strip()))
                            break
                except:
                    continue

            # Method 2: Large text at top of slide
            if not title_candidates:
                for shape in slide.shapes:
                    try:
                        if (hasattr(shape, "text") and shape.text.strip() and
                            hasattr(shape, "top") and hasattr(shape, "height")):
                            text = shape.text.strip()
                            # Potential title: at top, not too long, reasonable height
                            if (shape.top < 200 and len(text) < 150 and
                                shape.height > 20 and '\n' not in text):
                                title_candidates.append(("top_text", text))
                    except:
                        continue

            # Method 3: First significant text if no title found
            if not title_candidates:
                for shape in slide.shapes:
                    try:
                        if hasattr(shape, "text") and shape.text.strip():
                            text = shape.text.strip().split('\n')[0]  # First line only
                            if len(text) < 100 and len(text) > 5:
                                title_candidates.append(("first_text", text))
                                break
                    except:
                        continue

            # Select best title
            if title_candidates:
                slide_info["title"] = title_candidates[0][1]
            else:
                slide_info["title"] = f"Slide {i}"
            
            # Extract content with enhanced recursive approach (pass slide number for OCR)
            # Add slide number as attribute for OCR processing
            slide.slide_number = i
            shape_texts = extract_shapes_recursive(slide)

            # Enhanced detection and processing for image-heavy slides
            if not shape_texts and OCR_AVAILABLE:
                logger.info(f"No text found in slide {i}, checking if this is an image-based slide...")
                
                # Check if this slide contains primarily images
                image_shapes = 0
                total_shapes = len(slide.shapes)
                large_shapes = 0
                
                for shape in slide.shapes:
                    if hasattr(shape, 'shape_type'):
                        # Count potential image shapes
                        if shape.shape_type in [13, 14, 15, 16, 17, 18, 19]:  # Various image types
                            image_shapes += 1
                        
                    # Count large shapes that might be images
                    if (hasattr(shape, 'width') and hasattr(shape, 'height') and 
                        shape.width > 1000000 and shape.height > 1000000):  # Large shapes in EMUs
                        large_shapes += 1

                # Determine if this is likely an image-based slide
                is_image_slide = (
                    (image_shapes > 0) or  # Has identifiable image shapes
                    (large_shapes > 0 and total_shapes <= 3) or  # Has large shapes with few total shapes
                    (total_shapes == 1)  # Single shape (likely full-page image)
                )
                
                if is_image_slide:
                    logger.info(f"Slide {i} identified as image-based slide (images: {image_shapes}, large shapes: {large_shapes}, total: {total_shapes})")
                    
                    # Try aggressive OCR processing for image slides
                    shape_texts = extract_text_from_slide_aggressively(slide, i)
                    
                    if shape_texts:
                        logger.info(f"Aggressive OCR recovered {len(shape_texts)} text elements from image slide {i}")
                        
                        # Add special marking for image-based slides
                        slide_info["is_image_slide"] = True
                        slide_info["content_types"].append("Full Page Image")
                    else:
                        logger.warning(f"Image slide {i} processed but no readable text found")
                        # Still mark as image slide even if no text extracted
                        slide_info["is_image_slide"] = True
                        slide_info["content_types"].append("Image Only")
                        
                        # Add a placeholder to indicate slide was processed
                        shape_texts = [f"[IMAGE_SLIDE] Slide {i} contains images but no extractable text was found"]
                else:
                    logger.warning(f"Slide {i} has no text and doesn't appear to be image-based - may contain unsupported content")
                    shape_texts = [f"[NO_CONTENT] Slide {i} appears to have no readable content"]

            # Log extraction results for debugging
            logger.debug(f"Slide {i} extracted {len(shape_texts)} text elements from {slide_info['shape_count']} shapes")

            # Analyze content types and extract metadata
            content_analysis = {
                "text_elements": 0,
                "table_count": 0,
                "chart_count": 0,
                "image_count": 0,
                "image_text_count": 0,
                "bullet_points": 0,
                "numeric_data": 0
            }

            # Analyze extracted text for content types with enhanced image detection
            for text in shape_texts:
                if "[TABLE]" in text:
                    slide_info["has_table"] = True
                    content_analysis["table_count"] += 1
                    if "Table" not in slide_info["content_types"]:
                        slide_info["content_types"].append("Table")
                elif "[CHART]" in text:
                    slide_info["has_chart"] = True
                    content_analysis["chart_count"] += 1
                    if "Chart" not in slide_info["content_types"]:
                        slide_info["content_types"].append("Chart")
                elif "[IMAGE_TEXT]" in text or "[FULL_PAGE_IMAGE_TEXT]" in text:
                    content_analysis["image_text_count"] += 1
                    slide_info["has_image"] = True
                    if "Image Text" not in slide_info["content_types"]:
                        slide_info["content_types"].append("Image Text")
                elif "[IMAGE_SLIDE]" in text or "[NO_CONTENT]" in text:
                    # Special handling for image-only slides
                    if "Image Only" not in slide_info["content_types"]:
                        slide_info["content_types"].append("Image Only")
                else:
                    content_analysis["text_elements"] += 1

                # Count bullet points
                if text.count('•') > 0 or text.count('-') > 0:
                    content_analysis["bullet_points"] += text.count('•') + text.count('-')

                # Enhanced numeric data detection for insurance documents
                import re
                numeric_patterns = [
                    r'\$[\d,]+\.?\d*',  # Currency
                    r'\d+\.?\d*%',      # Percentages
                    r'\b\d{1,3}(?:,\d{3})*(?:\.\d+)?\b',  # Numbers with commas
                    r'\b\d+\.?\d*\b',   # Simple numbers
                    r'Policy\s*#?\s*\d+',  # Policy numbers
                    r'\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b',  # Dates
                ]
                for pattern in numeric_patterns:
                    content_analysis["numeric_data"] += len(re.findall(pattern, text, re.IGNORECASE))

            # Check for images and other visual elements
            for shape in slide.shapes:
                try:
                    # Check for images
                    if hasattr(shape, "shape_type"):
                        # Image shape types
                        if shape.shape_type in [13, 14]:  # PICTURE, LINKED_PICTURE
                            slide_info["has_image"] = True
                            content_analysis["image_count"] += 1
                            if "Image" not in slide_info["content_types"]:
                                slide_info["content_types"].append("Image")
                        # Other visual elements
                        elif (not hasattr(shape, "text") and not hasattr(shape, "has_table") and
                              not hasattr(shape, "has_chart")):
                            if hasattr(shape, "width") and hasattr(shape, "height"):
                                if shape.width > 100 and shape.height > 100:
                                    slide_info["has_image"] = True
                                    content_analysis["image_count"] += 1
                                    if "Visual" not in slide_info["content_types"]:
                                        slide_info["content_types"].append("Visual")
                except:
                    continue

            # Store extracted text and analysis
            slide_info["content"] = shape_texts
            slide_info["content_analysis"] = content_analysis
            slide_structure[i] = slide_info

            # Create rich, structured slide text for better LLM context
            slide_text_parts = []

            # Header with comprehensive metadata
            header = f"=== SLIDE {i}: {slide_info['title']} ==="
            slide_text_parts.append(header)

            # Metadata section
            metadata = []
            if slide_info["layout_name"]:
                metadata.append(f"Layout: {slide_info['layout_name']}")
            if slide_info["content_types"]:
                metadata.append(f"Contains: {', '.join(slide_info['content_types'])}")

            # Content statistics
            stats = []
            if content_analysis["text_elements"] > 0:
                stats.append(f"{content_analysis['text_elements']} text elements")
            if content_analysis["bullet_points"] > 0:
                stats.append(f"{content_analysis['bullet_points']} bullet points")
            if content_analysis["numeric_data"] > 0:
                stats.append(f"{content_analysis['numeric_data']} numeric values")
            if content_analysis["image_text_count"] > 0:
                stats.append(f"{content_analysis['image_text_count']} image texts")

            if metadata or stats:
                slide_text_parts.append(f"[{', '.join(metadata + stats)}]")

            # Content section with enhanced formatting
            if shape_texts:
                slide_text_parts.append("\nCONTENT:")

                # Separate different content types
                tables = []
                charts = []
                image_texts = []
                text_content = []

                for text in shape_texts:
                    clean_text_content = text.strip()
                    if clean_text_content.startswith("[TABLE]"):
                        tables.append(clean_text_content)
                    elif clean_text_content.startswith("[CHART]"):
                        charts.append(clean_text_content)
                    elif clean_text_content.startswith("[IMAGE_TEXT]"):
                        # Clean up the image text marker
                        image_text = clean_text_content.replace("[IMAGE_TEXT]", "").strip()
                        image_texts.append(image_text)
                    else:
                        text_content.append(clean_text_content)

                # Add text content first
                if text_content:
                    for text in text_content:
                        # Preserve existing bullet formatting or add it
                        if text.startswith('•') or text.startswith('-'):
                            slide_text_parts.append(f"{text}")
                        else:
                            # Check if it's a multi-line text block
                            if '\n' in text:
                                slide_text_parts.append(f"{text}")
                            else:
                                slide_text_parts.append(f"• {text}")

                # Add tables
                if tables:
                    slide_text_parts.append("\nTABLES:")
                    for table in tables:
                        slide_text_parts.append(f"{table}")

                # Add charts
                if charts:
                    slide_text_parts.append("\nCHARTS:")
                    for chart in charts:
                        slide_text_parts.append(f"{chart}")

                # Add image text (OCR extracted)
                if image_texts:
                    slide_text_parts.append("\nIMAGE TEXT (OCR):")
                    for img_text in image_texts:
                        slide_text_parts.append(f"• {img_text}")

            else:
                slide_text_parts.append("\n[No extractable text content]")
                logger.warning(f"No text content extracted from slide {i} in {filename}")

            # Combine all parts
            slide_text = "\n".join(slide_text_parts) + "\n\n"
            slide_texts.append(slide_text)

        # Combine all slide text
        full_text = "\n\n".join(slide_texts)

        # Log extraction summary
        logger.info(f"PowerPoint extraction summary for {filename}: {len(slide_texts)} slides processed, {len(full_text)} characters extracted")

        if not full_text.strip():
            logger.warning(f"No text content found in PowerPoint file {filename}")
            return [f"No text content found in PowerPoint file {filename}. The file may contain only images or the text extraction failed."]
            
        # Create comprehensive presentation analysis
        presentation_structure = {
            "filename": filename,
            "total_slides": total_slides,
            "has_tables": any(slide_structure[i]["has_table"] for i in slide_structure),
            "has_charts": any(slide_structure[i]["has_chart"] for i in slide_structure),
            "has_images": any(slide_structure[i]["has_image"] for i in slide_structure),
            "slide_titles": [slide_structure[i]["title"] for i in sorted(slide_structure.keys())],
            "total_tables": sum(slide_structure[i]["content_analysis"]["table_count"] for i in slide_structure),
            "total_charts": sum(slide_structure[i]["content_analysis"]["chart_count"] for i in slide_structure),
            "total_image_texts": sum(slide_structure[i]["content_analysis"]["image_text_count"] for i in slide_structure),
            "total_numeric_data": sum(slide_structure[i]["content_analysis"]["numeric_data"] for i in slide_structure),
            "total_bullet_points": sum(slide_structure[i]["content_analysis"]["bullet_points"] for i in slide_structure),
            "content_density": len(full_text) / total_slides if total_slides > 0 else 0
        }

        # Process the content into optimized chunks for enhanced Q&A
        content_chunks = []

        # Create comprehensive overview with rich metadata
        overview_parts = [
            f"POWERPOINT DOCUMENT ANALYSIS - {filename}",
            f"=" * 50,
            f"DOCUMENT OVERVIEW:",
            f"• Total slides: {total_slides}",
            f"• Content density: {presentation_structure['content_density']:.0f} characters per slide",
            f"• Tables: {presentation_structure['total_tables']}",
            f"• Charts: {presentation_structure['total_charts']}",
            f"• Image texts (OCR): {presentation_structure['total_image_texts']}",
            f"• Numeric data points: {presentation_structure['total_numeric_data']}",
            f"• Bullet points: {presentation_structure['total_bullet_points']}",
            "",
            "SLIDE STRUCTURE:",
        ]

        # Add slide titles with content indicators
        for i, title in enumerate(presentation_structure['slide_titles'][:15], 1):  # First 15 slides
            slide_info = slide_structure[i]
            indicators = []
            if slide_info["has_table"]: indicators.append("📊")
            if slide_info["has_chart"]: indicators.append("📈")
            if slide_info["has_image"]: indicators.append("🖼️")

            indicator_str = " ".join(indicators) if indicators else ""
            overview_parts.append(f"  {i}. {title} {indicator_str}")

        if len(presentation_structure['slide_titles']) > 15:
            overview_parts.append(f"  ... and {len(presentation_structure['slide_titles']) - 15} more slides")

        overview_text = "\n".join(overview_parts)
        content_chunks.append(clean_text(overview_text))

        # Create intelligent slide grouping for optimal context
        slide_groups = []
        current_group = []
        current_length = 0
        current_topics = set()

        # Analyze slides for topic grouping
        for slide_text in slide_texts:
            slide_length = len(slide_text)

            # Extract key terms for topic analysis
            slide_topics = set()
            text_lower = slide_text.lower()

            # Insurance-specific topic keywords
            topic_keywords = {
                'coverage': ['coverage', 'benefit', 'protection', 'covered'],
                'premium': ['premium', 'cost', 'price', 'payment', 'fee'],
                'claims': ['claim', 'filing', 'process', 'settlement'],
                'policy': ['policy', 'contract', 'agreement', 'terms'],
                'exclusions': ['exclusion', 'limitation', 'restriction', 'not covered'],
                'financial': ['amount', 'dollar', '$', 'percentage', '%', 'rate']
            }

            for topic, keywords in topic_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    slide_topics.add(topic)

            # Grouping logic: group by topic similarity and length
            should_group = (
                current_length + slide_length <= 2500 and  # Size limit
                (not current_topics or slide_topics.intersection(current_topics)) and  # Topic similarity
                len(current_group) < 4  # Max slides per group
            )

            if should_group and current_group:
                current_group.append(slide_text)
                current_length += slide_length
                current_topics.update(slide_topics)
            else:
                # Start new group
                if current_group:
                    slide_groups.append({
                        'content': "\n".join(current_group),
                        'topics': current_topics.copy(),
                        'slide_count': len(current_group)
                    })

                current_group = [slide_text]
                current_length = slide_length
                current_topics = slide_topics.copy()

        # Add final group
        if current_group:
            slide_groups.append({
                'content': "\n".join(current_group),
                'topics': current_topics.copy(),
                'slide_count': len(current_group)
            })

        # Create enhanced content chunks with topic metadata
        for i, group in enumerate(slide_groups):
            chunk_header = f"POWERPOINT SECTION {i+1} of {len(slide_groups)} - {filename}"

            if group['topics']:
                topics_str = ", ".join(sorted(group['topics']))
                chunk_header += f"\nTopics: {topics_str}"

            chunk_header += f"\nSlides in section: {group['slide_count']}"
            chunk_header += "\n" + "="*60 + "\n"

            chunk_text = chunk_header + group['content']
            content_chunks.append(clean_text(chunk_text))

        # Add a summary chunk for quick reference
        if len(slide_groups) > 1:
            summary_parts = [
                f"POWERPOINT QUICK REFERENCE - {filename}",
                "="*50,
                f"This presentation contains {total_slides} slides organized into {len(slide_groups)} sections:",
                ""
            ]

            for i, group in enumerate(slide_groups):
                topics_str = ", ".join(sorted(group['topics'])) if group['topics'] else "General content"
                summary_parts.append(f"Section {i+1}: {group['slide_count']} slides - {topics_str}")

            summary_parts.extend([
                "",
                f"Key statistics:",
                f"• {presentation_structure['total_tables']} tables with structured data",
                f"• {presentation_structure['total_charts']} charts and visualizations",
                f"• {presentation_structure['total_image_texts']} image texts extracted via OCR",
                f"• {presentation_structure['total_numeric_data']} numeric data points",
                f"• {presentation_structure['total_bullet_points']} bullet points",
                "",
                "Use this reference to understand the presentation structure for answering questions."
            ])

            summary_text = "\n".join(summary_parts)
            content_chunks.insert(1, clean_text(summary_text))  # Insert after overview

        # Log successful processing with detailed stats
        logger.info(f"PowerPoint processing completed: {len(content_chunks)} chunks created from {total_slides} slides")
        logger.info(f"Content analysis: {presentation_structure['total_tables']} tables, {presentation_structure['total_charts']} charts, {presentation_structure['total_image_texts']} image texts, {presentation_structure['total_numeric_data']} numeric values")

        return content_chunks

    except Exception as e:
        logger.error(f"Error processing PowerPoint with LLM: {e}")
        return [f"Error processing PowerPoint {filename}: {str(e)}"]


def extract_text_from_url(url):
    # Ensure URL is properly formatted and encoded
    try:
        # Try to fetch the document
        response = requests.get(url, timeout=60)
        response.raise_for_status()  # Raise exception for 4xx/5xx responses
        content_type = response.headers.get("Content-Type", "").lower()

        # Extract filename from URL, handling query parameters
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        if path_parts and path_parts[-1]:
            filename = path_parts[-1].lower()
        else:
            # Use a default filename if path doesn't have one
            filename = "document.pdf" if "pdf" in content_type else "document"

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(response.content)
            tmp_path = tmp.name
    except Exception as e:
        raise e

    # Check for ZIP files first (handle without LLM)
    if is_zip_url(url):
        try:
            os.unlink(tmp_path)  # Clean up temp file
            zip_result = process_zip_from_url(url)
            if zip_result.get("success", False):
                return [clean_text(zip_result.get("summary", "ZIP file processed successfully"))]
            else:
                return [f"Error processing ZIP file: {zip_result.get('error', 'Unknown error')}"]
        except Exception as e:
            return [f"Error processing ZIP file: {str(e)}"]

    # Check for Excel/CSV files (handle without LLM)
    if is_excel_or_csv_url(url):
        try:
            os.unlink(tmp_path)  # Clean up temp file
            data_result = process_excel_csv_from_url(url)
            if data_result.get("success", False):
                return [clean_text(data_result.get("summary", "Excel/CSV file processed successfully"))]
            else:
                return [f"Error processing Excel/CSV file: {data_result.get('error', 'Unknown error')}"]
        except Exception as e:
            return [f"Error processing Excel/CSV file: {str(e)}"]

    # Check for image files (handle with LLM)
    if is_image_file(filename, content_type):
        try:
            result = process_image_with_llm(tmp_path, filename)
            os.unlink(tmp_path)  # Clean up temp file
            return result
        except Exception as e:
            os.unlink(tmp_path)  # Clean up temp file
            return [f"Error processing image file: {str(e)}"]

    # Check for PowerPoint files (extract content for direct Q&A)
    if is_powerpoint_file(filename, content_type):
        try:
            result = process_powerpoint_for_qa(tmp_path, filename)
            os.unlink(tmp_path)  # Clean up temp file
            return result
        except Exception as e:
            os.unlink(tmp_path)  # Clean up temp file
            return [f"Error processing PowerPoint file: {str(e)}"]

    # Handle traditional document types
    if "pdf" in content_type or filename.endswith(".pdf"):
        # Enhanced PDF processing with insurance domain-specific enhancements
        doc = fitz.open(tmp_path)
        text_pages = []

        # Insurance-specific metadata to track
        insurance_metadata = {
            "has_policy_info": False,
            "has_coverage_details": False,
            "has_premium_info": False,
            "has_claim_process": False,
            "has_exclusions": False,
            "has_definitions": False,
            "has_contact_info": False,
            "has_declarations": False,
            "has_endorsements": False,
            "insurance_document_type": "unknown"
        }
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)', re.IGNORECASE)
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?', re.IGNORECASE)
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?', re.IGNORECASE)
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?', re.IGNORECASE)
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)', re.IGNORECASE)
        definition_pattern = re.compile(r'(?i)(definition|glossary|meaning|defined term)', re.IGNORECASE)
        contact_pattern = re.compile(r'(?i)(contact|phone|email|fax|address|customer service)', re.IGNORECASE)
        declaration_pattern = re.compile(r'(?i)(declaration|schedule|statement|summary|overview|policy detail)', re.IGNORECASE)
        endorsement_pattern = re.compile(r'(?i)(endorsement|rider|amendment|addendum|schedule)', re.IGNORECASE)
        
        # Document type indicators
        doc_type_patterns = {
            "policy": re.compile(r'(?i)(insurance policy|policy document|certificate of insurance)'),
            "quote": re.compile(r'(?i)(insurance quote|premium quote|price quote|proposal)'),
            "claim": re.compile(r'(?i)(claim form|notice of claim|claim application)'),
            "endorsement": re.compile(r'(?i)(policy endorsement|policy rider|amendment to policy)'),
            "renewal": re.compile(r'(?i)(renewal notice|policy renewal|renewal offer)'),
            "bill": re.compile(r'(?i)(premium notice|bill|invoice|statement|payment due)'),
            "cancellation": re.compile(r'(?i)(cancellation notice|policy termination)'),
            "explanation": re.compile(r'(?i)(explanation of benefits|EOB|benefit statement)')
        }
        
        # Extract regular text with insurance context awareness
        for page_num, page in enumerate(doc, 1):
            page_text = page.get_text()
            clean_page_text = clean_text(page_text)
            
            # Detect insurance-specific content
            if policy_pattern.search(page_text):
                insurance_metadata["has_policy_info"] = True
                
            if coverage_pattern.search(page_text):
                insurance_metadata["has_coverage_details"] = True
                
            if premium_pattern.search(page_text):
                insurance_metadata["has_premium_info"] = True
                
            if claim_pattern.search(page_text):
                insurance_metadata["has_claim_process"] = True
                
            if exclusion_pattern.search(page_text):
                insurance_metadata["has_exclusions"] = True
                
            if definition_pattern.search(page_text):
                insurance_metadata["has_definitions"] = True
                
            if contact_pattern.search(page_text):
                insurance_metadata["has_contact_info"] = True
                
            if declaration_pattern.search(page_text):
                insurance_metadata["has_declarations"] = True
                
            if endorsement_pattern.search(page_text):
                insurance_metadata["has_endorsements"] = True
            
            # Detect document type
            for doc_type, pattern in doc_type_patterns.items():
                if pattern.search(page_text):
                    insurance_metadata["insurance_document_type"] = doc_type
                    break
                    
            # Add insurance metadata context to first page
            if page_num == 1 and any(insurance_metadata.values()):
                metadata_prefix = "INSURANCE DOCUMENT ANALYSIS:\n"
                
                # Add document type if detected
                if insurance_metadata["insurance_document_type"] != "unknown":
                    metadata_prefix += f"Document Type: {insurance_metadata['insurance_document_type'].title()}\n"
                
                # Add content type indicators
                content_types = []
                if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
                if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details") 
                if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
                if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
                if insurance_metadata["has_exclusions"]: content_types.append("Exclusions")
                if insurance_metadata["has_definitions"]: content_types.append("Definitions")
                if insurance_metadata["has_declarations"]: content_types.append("Declarations")
                if insurance_metadata["has_endorsements"]: content_types.append("Endorsements")
                
                if content_types:
                    metadata_prefix += f"Contains: {', '.join(content_types)}\n\n"
                    
                # Add the metadata as a prefix to the first page text
                clean_page_text = metadata_prefix + clean_page_text
            
            text_pages.append(clean_page_text)

        doc.close()

        # Extract tabular data with insurance context awareness
        try:
            tabular_content = extract_tabular_data_from_pdf(tmp_path)
            if tabular_content:
                # Add insurance-specific context to tables
                enhanced_tables = []
                for table_text in tabular_content:
                    # Check if this table appears to be coverage, premium, or benefit related
                    table_lower = table_text.lower()
                    
                    table_context = ""
                    if any(term in table_lower for term in ["coverage", "cover", "covered", "benefit", "protection"]):
                        table_context = "[COVERAGE TABLE] "
                    elif any(term in table_lower for term in ["premium", "payment", "cost", "price", "rate"]):
                        table_context = "[PREMIUM TABLE] "
                    elif any(term in table_lower for term in ["limit", "maximum", "deductible", "copay", "coinsurance"]):
                        table_context = "[FINANCIAL DETAILS TABLE] "
                    elif any(term in table_lower for term in ["exclusion", "exclude", "not covered", "limitation"]):
                        table_context = "[EXCLUSION TABLE] "
                    
                    enhanced_table = table_context + enhanced_text_preprocessing(table_text)
                    enhanced_tables.append(enhanced_table)
                    
                # Add enhanced tabular content
                text_pages.extend(enhanced_tables)
                logger.info(f"Extracted {len(tabular_content)} insurance-related tables from PDF")
        except Exception as e:
            logger.warning(f"Table extraction failed: {e}")

        os.unlink(tmp_path)
        return text_pages

    elif "word" in content_type or filename.endswith(".docx"):
        # Enhanced DOCX processing with insurance domain-specific optimizations
        doc = Document(tmp_path)
        
        # Insurance-specific metadata to track
        insurance_metadata = {
            "has_policy_info": False,
            "has_coverage_details": False,
            "has_premium_info": False,
            "has_claim_process": False,
            "has_exclusions": False,
            "has_definitions": False,
            "has_tables": False,
            "insurance_document_type": "unknown",
            "headings": []
        }
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)')
        definition_pattern = re.compile(r'(?i)(definition|glossary|meaning|defined term)')
        
        # Document type indicators
        doc_type_patterns = {
            "policy": re.compile(r'(?i)(insurance policy|policy document|certificate of insurance)'),
            "quote": re.compile(r'(?i)(insurance quote|premium quote|price quote|proposal)'),
            "claim": re.compile(r'(?i)(claim form|notice of claim|claim application)'),
            "endorsement": re.compile(r'(?i)(policy endorsement|policy rider|amendment to policy)'),
            "renewal": re.compile(r'(?i)(renewal notice|policy renewal|renewal offer)'),
            "bill": re.compile(r'(?i)(premium notice|bill|invoice|statement|payment due)')
        }
        
        # Extract paragraphs with insurance context awareness
        enhanced_paragraphs = []
        headings = []
        
        # Process paragraphs
        for para in doc.paragraphs:
            # Skip empty paragraphs
            if not para.text.strip():
                continue
                
            # Detect headings based on style name
            is_heading = False
            if para.style.name.startswith('Heading'):
                is_heading = True
                headings.append(para.text.strip())
                insurance_metadata["headings"].append(para.text.strip())
            
            # Check for insurance-specific content
            para_text = para.text
            
            if policy_pattern.search(para_text):
                insurance_metadata["has_policy_info"] = True
                
            if coverage_pattern.search(para_text):
                insurance_metadata["has_coverage_details"] = True
                
            if premium_pattern.search(para_text):
                insurance_metadata["has_premium_info"] = True
                
            if claim_pattern.search(para_text):
                insurance_metadata["has_claim_process"] = True
                
            if exclusion_pattern.search(para_text):
                insurance_metadata["has_exclusions"] = True
                
            if definition_pattern.search(para_text):
                insurance_metadata["has_definitions"] = True
            
            # Detect document type
            for doc_type, pattern in doc_type_patterns.items():
                if pattern.search(para_text):
                    insurance_metadata["insurance_document_type"] = doc_type
                    break
            
            # Enhance headings with context
            if is_heading:
                heading_text = para.text.strip()
                
                # Identify the type of heading
                heading_type = ""
                if any(term in heading_text.lower() for term in ["coverage", "cover", "covered", "benefit"]):
                    heading_type = "[COVERAGE SECTION] "
                elif any(term in heading_text.lower() for term in ["exclusion", "not covered", "limitation"]):
                    heading_type = "[EXCLUSION SECTION] "
                elif any(term in heading_text.lower() for term in ["definition", "glossary", "term", "meaning"]):
                    heading_type = "[DEFINITION SECTION] "
                elif any(term in heading_text.lower() for term in ["claim", "procedure", "process", "filing"]):
                    heading_type = "[CLAIMS SECTION] "
                elif any(term in heading_text.lower() for term in ["premium", "payment", "cost", "price"]):
                    heading_type = "[PREMIUM SECTION] "
                    
                    # Add enhanced heading
                enhanced_paragraphs.append(heading_type + heading_text)
            else:
                enhanced_paragraphs.append(para.text)
        
        # Process tables in the document
        table_texts = []
        for i, table in enumerate(doc.tables):
            insurance_metadata["has_tables"] = True
            
            table_text = f"Table {i+1}:\n"
            table_content = ""
            
            # Extract table content
            for row in table.rows:
                row_text = " | ".join(cell.text.strip() for cell in row.cells)
                table_content += row_text + "\n"
                
            # Determine table type
            table_type = ""
            table_lower = table_content.lower()
            
            if any(term in table_lower for term in ["coverage", "cover", "covered", "benefit", "protection"]):
                table_type = "[COVERAGE TABLE] "
            elif any(term in table_lower for term in ["premium", "payment", "cost", "price", "rate"]):
                table_type = "[PREMIUM TABLE] "
            elif any(term in table_lower for term in ["limit", "maximum", "deductible", "copay", "coinsurance"]):
                table_type = "[FINANCIAL DETAILS TABLE] "
            elif any(term in table_lower for term in ["exclusion", "exclude", "not covered", "limitation"]):
                table_type = "[EXCLUSION TABLE] "
                
            table_texts.append(table_type + table_text + table_content)
        
        # Combine all content with insurance metadata
        document_content = "\n".join(enhanced_paragraphs + table_texts)
        
        # Add insurance metadata prefix if relevant content found
        if any([insurance_metadata["has_policy_info"], 
                insurance_metadata["has_coverage_details"],
                insurance_metadata["has_premium_info"],
                insurance_metadata["has_claim_process"],
                insurance_metadata["has_exclusions"],
                insurance_metadata["has_definitions"],
                insurance_metadata["insurance_document_type"] != "unknown"]):
            
            metadata_prefix = "INSURANCE DOCUMENT ANALYSIS:\n"
            
            # Add document type if detected
            if insurance_metadata["insurance_document_type"] != "unknown":
                metadata_prefix += f"Document Type: {insurance_metadata['insurance_document_type'].title()}\n"
            
            # Add content type indicators
            content_types = []
            if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
            if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details") 
            if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
            if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
            if insurance_metadata["has_exclusions"]: content_types.append("Exclusions")
            if insurance_metadata["has_definitions"]: content_types.append("Definitions")
            if insurance_metadata["has_tables"]: content_types.append("Tables")
            
            if content_types:
                metadata_prefix += f"Contains: {', '.join(content_types)}\n"
                
            # Add document structure if headings found
            if insurance_metadata["headings"]:
                metadata_prefix += f"Document Structure: {len(insurance_metadata['headings'])} sections including "
                metadata_prefix += ", ".join(insurance_metadata["headings"][:3])
                if len(insurance_metadata["headings"]) > 3:
                    metadata_prefix += f" and {len(insurance_metadata['headings']) - 3} more"
                metadata_prefix += "\n"
                
            metadata_prefix += "\n"
            
            # Add metadata prefix to content
            document_content = metadata_prefix + document_content
        
        # Clean up
        os.unlink(tmp_path)
        
        # Return processed content
        return [clean_text(document_content)]

    elif "text/plain" in content_type or filename.endswith(".txt"):
        # Enhanced text file processing with insurance domain-specific optimizations
        raw_text = response.text
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)')
        definition_pattern = re.compile(r'(?i)(definition|glossary|meaning|defined term)')
        
        # Track insurance-specific metadata
        insurance_metadata = {
            "has_policy_info": bool(policy_pattern.search(raw_text)),
            "has_coverage_details": bool(coverage_pattern.search(raw_text)),
            "has_premium_info": bool(premium_pattern.search(raw_text)),
            "has_claim_process": bool(claim_pattern.search(raw_text)),
            "has_exclusions": bool(exclusion_pattern.search(raw_text)),
            "has_definitions": bool(definition_pattern.search(raw_text))
        }
        
        # Detect document type
        doc_type_patterns = {
            "policy": re.compile(r'(?i)(insurance policy|policy document|certificate of insurance)'),
            "quote": re.compile(r'(?i)(insurance quote|premium quote|price quote|proposal)'),
            "claim": re.compile(r'(?i)(claim form|notice of claim|claim application)'),
            "endorsement": re.compile(r'(?i)(policy endorsement|policy rider|amendment to policy)'),
            "renewal": re.compile(r'(?i)(renewal notice|policy renewal|renewal offer)'),
            "bill": re.compile(r'(?i)(premium notice|bill|invoice|statement|payment due)')
        }
        
        doc_type = "unknown"
        for dtype, pattern in doc_type_patterns.items():
            if pattern.search(raw_text):
                doc_type = dtype
                break
                
        # Extract key sections with insurance-specific context
        sections = []
        
        # Look for policy information
        if insurance_metadata["has_policy_info"]:
            # Extract policy number if present
            policy_matches = list(policy_pattern.finditer(raw_text))
            for match in policy_matches:
                policy_context = raw_text[max(0, match.start() - 50):min(len(raw_text), match.end() + 50)]
                sections.append("[POLICY INFORMATION] " + clean_text(policy_context))
        
        # Look for coverage sections
        if insurance_metadata["has_coverage_details"]:
            coverage_matches = list(coverage_pattern.finditer(raw_text))
            for match in coverage_matches[:2]:  # Limit to first 2 matches to avoid redundancy
                coverage_context = raw_text[max(0, match.start() - 50):min(len(raw_text), match.end() + 100)]
                sections.append("[COVERAGE DETAILS] " + clean_text(coverage_context))
                
        # Add metadata prefix if insurance content detected
        if any(insurance_metadata.values()) or doc_type != "unknown":
            metadata_prefix = "INSURANCE DOCUMENT ANALYSIS:\n"
            
            # Add document type if detected
            if doc_type != "unknown":
                metadata_prefix += f"Document Type: {doc_type.title()}\n"
            
            # Add content type indicators
            content_types = []
            if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
            if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details") 
            if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
            if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
            if insurance_metadata["has_exclusions"]: content_types.append("Exclusions")
            if insurance_metadata["has_definitions"]: content_types.append("Definitions")
            
            if content_types:
                metadata_prefix += f"Contains: {', '.join(content_types)}\n\n"
                
            # Process full text with metadata
            processed_text = metadata_prefix + clean_text(raw_text)
            
            # Return text with metadata
            os.unlink(tmp_path)
            return [processed_text] + sections if sections else [processed_text]
        else:
            # No insurance content detected, return regular cleaned text
            os.unlink(tmp_path)
            return [clean_text(raw_text)]

    elif "html" in content_type or filename.endswith(".html"):
        # Enhanced HTML processing with insurance domain-specific optimizations
        soup = BeautifulSoup(response.text, "lxml")
        
        # Extract title if present
        title = soup.title.string if soup.title else None
        
        # Extract all text
        full_text = soup.get_text(separator="\n")
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)')
        definition_pattern = re.compile(r'(?i)(definition|glossary|meaning|defined term)')
        
        # Track insurance-specific metadata
        insurance_metadata = {
            "has_policy_info": bool(policy_pattern.search(full_text)),
            "has_coverage_details": bool(coverage_pattern.search(full_text)),
            "has_premium_info": bool(premium_pattern.search(full_text)),
            "has_claim_process": bool(claim_pattern.search(full_text)),
            "has_exclusions": bool(exclusion_pattern.search(full_text)),
            "has_definitions": bool(definition_pattern.search(full_text)),
            "has_tables": len(soup.find_all('table')) > 0
        }
        
        # Extract important section titles from HTML
        section_titles = []
        for heading in soup.find_all(['h1', 'h2', 'h3']):
            if heading.text.strip():
                section_titles.append(heading.text.strip())
                
        # Find tables and extract with context
        tables = []
        for i, table in enumerate(soup.find_all('table')):
            table_html = str(table)
            table_text = table.get_text(separator=' | ', strip=True)
            
            # Determine table type
            table_type = ""
            table_lower = table_text.lower()
            
            if any(term in table_lower for term in ["coverage", "cover", "covered", "benefit", "protection"]):
                table_type = "[COVERAGE TABLE] "
            elif any(term in table_lower for term in ["premium", "payment", "cost", "price", "rate"]):
                table_type = "[PREMIUM TABLE] "
            elif any(term in table_lower for term in ["limit", "maximum", "deductible", "copay", "coinsurance"]):
                table_type = "[FINANCIAL DETAILS TABLE] "
            elif any(term in table_lower for term in ["exclusion", "exclude", "not covered", "limitation"]):
                table_type = "[EXCLUSION TABLE] "
                
            tables.append(f"{table_type}Table {i+1}:\n{table_text}")
            
        # Detect document type
        doc_type_patterns = {
            "policy": re.compile(r'(?i)(insurance policy|policy document|certificate of insurance)'),
            "quote": re.compile(r'(?i)(insurance quote|premium quote|price quote|proposal)'),
            "claim": re.compile(r'(?i)(claim form|notice of claim|claim application)'),
            "endorsement": re.compile(r'(?i)(policy endorsement|policy rider|amendment to policy)'),
            "renewal": re.compile(r'(?i)(renewal notice|policy renewal|renewal offer)'),
            "bill": re.compile(r'(?i)(premium notice|bill|invoice|statement|payment due)')
        }
        
        doc_type = "unknown"
        for dtype, pattern in doc_type_patterns.items():
            if pattern.search(full_text):
                doc_type = dtype
                break
        
        # Build insurance-specific metadata
        if any(insurance_metadata.values()) or doc_type != "unknown" or (title and any(term in title.lower() for term in ["insurance", "policy", "coverage", "claim"])):
            metadata_prefix = "INSURANCE DOCUMENT ANALYSIS:\n"
            
            # Add title if present
            if title:
                metadata_prefix += f"Document Title: {title}\n"
                
            # Add document type if detected
            if doc_type != "unknown":
                metadata_prefix += f"Document Type: {doc_type.title()}\n"
            
            # Add content type indicators
            content_types = []
            if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
            if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details") 
            if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
            if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
            if insurance_metadata["has_exclusions"]: content_types.append("Exclusions")
            if insurance_metadata["has_definitions"]: content_types.append("Definitions")
            if insurance_metadata["has_tables"]: content_types.append("Tables")
            
            if content_types:
                metadata_prefix += f"Contains: {', '.join(content_types)}\n"
            
            # Add section structure if headings found
            if section_titles:
                metadata_prefix += f"Document Structure: {len(section_titles)} sections including "
                metadata_prefix += ", ".join(section_titles[:3])
                if len(section_titles) > 3:
                    metadata_prefix += f" and {len(section_titles) - 3} more"
                metadata_prefix += "\n"
                
            metadata_prefix += "\n"
            
            # Return text with metadata and tables
            cleaned_full_text = clean_text(full_text)
            result = [metadata_prefix + cleaned_full_text]
            
            # Add extracted tables as separate chunks if present
            if tables:
                result.extend(tables)
                
            os.unlink(tmp_path)
            return result
        else:
            # No insurance content detected, return regular cleaned text
            os.unlink(tmp_path)
            return [clean_text(full_text)]

    elif filename.endswith(".eml"):
        # Enhanced EML processing with insurance domain-specific optimizations
        with open(tmp_path, "rb") as f:
            msg = email.message_from_binary_file(f, policy=policy.default)
        
        # Extract email metadata
        email_metadata = {
            "subject": msg.get("Subject", ""),
            "from": msg.get("From", ""),
            "to": msg.get("To", ""),
            "date": msg.get("Date", ""),
            "has_attachments": False
        }
        
        # Extract email body
        body = ""
        if msg.is_multipart():
            # Check if email has attachments
            for part in msg.walk():
                if part.get_content_disposition() == 'attachment':
                    email_metadata["has_attachments"] = True
                    continue
                    
                if part.get_content_type() == "text/plain":
                    body += part.get_payload(decode=True).decode(errors="ignore")
        else:
            body = msg.get_payload(decode=True).decode(errors="ignore")
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)')
        definition_pattern = re.compile(r'(?i)(definition|glossary|meaning|defined term)')
        
        # Insurance email specific patterns
        renewal_pattern = re.compile(r'(?i)(renew|renewal|extension|continue|policy term)')
        cancellation_pattern = re.compile(r'(?i)(cancel|termination|end|expire|discontinue|lapse)')
        payment_reminder_pattern = re.compile(r'(?i)(reminder|due|overdue|payment|outstanding|invoice)')
        claim_status_pattern = re.compile(r'(?i)(status|update|progress|approved|denied|review|process)')
        
        # Track insurance-specific metadata
        insurance_metadata = {
            "has_policy_info": bool(policy_pattern.search(body)) or bool(policy_pattern.search(email_metadata["subject"])),
            "has_coverage_details": bool(coverage_pattern.search(body)),
            "has_premium_info": bool(premium_pattern.search(body)),
            "has_claim_process": bool(claim_pattern.search(body)) or bool(claim_pattern.search(email_metadata["subject"])),
            "has_renewal_info": bool(renewal_pattern.search(body)) or bool(renewal_pattern.search(email_metadata["subject"])),
            "has_cancellation_info": bool(cancellation_pattern.search(body)) or bool(cancellation_pattern.search(email_metadata["subject"])),
            "has_payment_reminder": bool(payment_reminder_pattern.search(body)) or bool(payment_reminder_pattern.search(email_metadata["subject"])),
            "has_claim_status": bool(claim_status_pattern.search(body)) or bool(claim_status_pattern.search(email_metadata["subject"]))
        }
        
        # Detect email type
        email_type = "general"
        if insurance_metadata["has_renewal_info"]:
            email_type = "policy_renewal"
        elif insurance_metadata["has_cancellation_info"]:
            email_type = "policy_cancellation"
        elif insurance_metadata["has_payment_reminder"]:
            email_type = "payment_reminder"
        elif insurance_metadata["has_claim_status"]:
            email_type = "claim_status_update"
        elif insurance_metadata["has_claim_process"]:
            email_type = "claim_process"
        elif insurance_metadata["has_policy_info"]:
            email_type = "policy_information"
        
        # Extract policy numbers if present
        policy_numbers = []
        for match in policy_pattern.finditer(body):
            if match.group(2):  # Capture group 2 contains the policy number
                policy_numbers.append(match.group(2))
        
        # Add insurance metadata prefix
        metadata_prefix = "INSURANCE EMAIL ANALYSIS:\n"
        metadata_prefix += f"Subject: {email_metadata['subject']}\n"
        metadata_prefix += f"From: {email_metadata['from']}\n"
        metadata_prefix += f"To: {email_metadata['to']}\n"
        metadata_prefix += f"Date: {email_metadata['date']}\n"
        metadata_prefix += f"Has Attachments: {'Yes' if email_metadata['has_attachments'] else 'No'}\n"
        
        if email_type != "general":
            metadata_prefix += f"Email Type: {email_type.replace('_', ' ').title()}\n"
            
        if policy_numbers:
            metadata_prefix += f"Referenced Policy Numbers: {', '.join(policy_numbers)}\n"
        
        # Add content type indicators
        content_types = []
        if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
        if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details")
        if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
        if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
        if insurance_metadata["has_renewal_info"]: content_types.append("Renewal Information")
        if insurance_metadata["has_cancellation_info"]: content_types.append("Cancellation Information")
        if insurance_metadata["has_payment_reminder"]: content_types.append("Payment Reminder")
        if insurance_metadata["has_claim_status"]: content_types.append("Claim Status Update")
        
        if content_types:
            metadata_prefix += f"Contains: {', '.join(content_types)}\n"
            
        metadata_prefix += "\n"
        
        # Combine metadata with body
        processed_email = metadata_prefix + clean_text(body)
        
        os.unlink(tmp_path)
        return [processed_email]

    elif filename.endswith(".msg"):
        # Enhanced MSG processing with insurance domain-specific optimizations
        msg = extract_msg.Message(tmp_path)
        
        # Extract email metadata
        email_metadata = {
            "subject": msg.subject,
            "sender": msg.sender,
            "date": msg.date,
            "recipients": msg.to if hasattr(msg, 'to') else "",
            "has_attachments": len(msg.attachments) > 0 if hasattr(msg, 'attachments') else False
        }
        
        # Get email body
        body = msg.body
        
        # Insurance-specific regex patterns
        policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
        coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
        premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
        claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
        exclusion_pattern = re.compile(r'(?i)(exclusion|excluded|not covered|limitation|restrict|exception)')
        
        # Insurance email specific patterns
        renewal_pattern = re.compile(r'(?i)(renew|renewal|extension|continue|policy term)')
        cancellation_pattern = re.compile(r'(?i)(cancel|termination|end|expire|discontinue|lapse)')
        payment_reminder_pattern = re.compile(r'(?i)(reminder|due|overdue|payment|outstanding|invoice)')
        claim_status_pattern = re.compile(r'(?i)(status|update|progress|approved|denied|review|process)')
        
        # Track insurance-specific metadata
        insurance_metadata = {
            "has_policy_info": bool(policy_pattern.search(body)) or bool(policy_pattern.search(email_metadata["subject"])),
            "has_coverage_details": bool(coverage_pattern.search(body)),
            "has_premium_info": bool(premium_pattern.search(body)),
            "has_claim_process": bool(claim_pattern.search(body)) or bool(claim_pattern.search(email_metadata["subject"])),
            "has_renewal_info": bool(renewal_pattern.search(body)) or bool(renewal_pattern.search(email_metadata["subject"])),
            "has_cancellation_info": bool(cancellation_pattern.search(body)) or bool(cancellation_pattern.search(email_metadata["subject"])),
            "has_payment_reminder": bool(payment_reminder_pattern.search(body)) or bool(payment_reminder_pattern.search(email_metadata["subject"])),
            "has_claim_status": bool(claim_status_pattern.search(body)) or bool(claim_status_pattern.search(email_metadata["subject"]))
        }
        
        # Detect email type
        email_type = "general"
        if insurance_metadata["has_renewal_info"]:
            email_type = "policy_renewal"
        elif insurance_metadata["has_cancellation_info"]:
            email_type = "policy_cancellation"
        elif insurance_metadata["has_payment_reminder"]:
            email_type = "payment_reminder"
        elif insurance_metadata["has_claim_status"]:
            email_type = "claim_status_update"
        elif insurance_metadata["has_claim_process"]:
            email_type = "claim_process"
        elif insurance_metadata["has_policy_info"]:
            email_type = "policy_information"
        
        # Extract policy numbers if present
        policy_numbers = []
        for match in policy_pattern.finditer(body):
            if match.group(2):  # Capture group 2 contains the policy number
                policy_numbers.append(match.group(2))
        
        # Add insurance metadata prefix
        metadata_prefix = "INSURANCE EMAIL ANALYSIS:\n"
        metadata_prefix += f"Subject: {email_metadata['subject']}\n"
        metadata_prefix += f"Sender: {email_metadata['sender']}\n"
        metadata_prefix += f"Recipients: {email_metadata['recipients']}\n"
        metadata_prefix += f"Date: {email_metadata['date']}\n"
        metadata_prefix += f"Has Attachments: {'Yes' if email_metadata['has_attachments'] else 'No'}\n"
        
        if email_type != "general":
            metadata_prefix += f"Email Type: {email_type.replace('_', ' ').title()}\n"
            
        if policy_numbers:
            metadata_prefix += f"Referenced Policy Numbers: {', '.join(policy_numbers)}\n"
        
        # Add content type indicators
        content_types = []
        if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
        if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details")
        if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
        if insurance_metadata["has_claim_process"]: content_types.append("Claim Process")
        if insurance_metadata["has_renewal_info"]: content_types.append("Renewal Information")
        if insurance_metadata["has_cancellation_info"]: content_types.append("Cancellation Information")
        if insurance_metadata["has_payment_reminder"]: content_types.append("Payment Reminder")
        if insurance_metadata["has_claim_status"]: content_types.append("Claim Status Update")
        
        if content_types:
            metadata_prefix += f"Contains: {', '.join(content_types)}\n"
            
        metadata_prefix += "\n"
        
        # Combine metadata with body
        processed_email = metadata_prefix + clean_text(body)
        
        os.unlink(tmp_path)
        return [processed_email]
        
    elif filename.endswith(".bin"):
        # Process binary file with domain-specific insurance optimizations
        logger.info(f"Processing binary file: {filename}")
        
        try:
            # Read file in binary mode
            with open(tmp_path, 'rb') as bin_file:
                binary_data = bin_file.read()
            
            file_size = len(binary_data)
            logger.info(f"Binary file size: {file_size} bytes")
            
            # Attempt to decode the first few bytes to check for text content
            text_sample = ""
            try:
                # Try to decode the first 8KB as text to look for meaningful content
                text_sample = binary_data[:8192].decode('utf-8', errors='ignore')
            except Exception as e:
                logger.warning(f"Error decoding binary file: {e}")
            
            # Insurance-specific regex patterns
            policy_pattern = re.compile(r'(?i)policy\s*(number|#|no|id)\s*[:.\-]?\s*([A-Z0-9][\w\-]*\d+)')
            coverage_pattern = re.compile(r'(?i)(coverage|covered|protection|benefit|limit)s?\s*(amount|limit|sum)?')
            premium_pattern = re.compile(r'(?i)(premium|payment|installment|cost|price)\s*(amount|due|total)?')
            claim_pattern = re.compile(r'(?i)(claim|filing|submit|process)\s*(procedure|process|form|request)?')
            policy_doc_pattern = re.compile(r'(?i)(insurance policy|certificate|declaration|endorsement)')
            
            # Check for insurance-related content in the text sample
            insurance_metadata = {
                "has_policy_info": bool(policy_pattern.search(text_sample)),
                "has_coverage_details": bool(coverage_pattern.search(text_sample)),
                "has_premium_info": bool(premium_pattern.search(text_sample)),
                "has_claim_info": bool(claim_pattern.search(text_sample)),
                "has_policy_document": bool(policy_doc_pattern.search(text_sample))
            }
            
            # Generate basic file signature by checking first few bytes
            file_signature = ""
            if len(binary_data) >= 4:
                signature_bytes = binary_data[:4]
                file_signature = " ".join([f"{b:02X}" for b in signature_bytes])
            
            # Determine if this is likely an insurance-related binary file
            has_insurance_content = any(insurance_metadata.values())
            
            # Attempt to identify common binary file types
            bin_file_type = "unknown binary"
            if binary_data.startswith(b'%PDF'):
                bin_file_type = "PDF (binary mode)"
            elif binary_data.startswith(b'PK\x03\x04'):
                bin_file_type = "ZIP archive or Office document"
            elif binary_data.startswith(b'\x50\x4B\x03\x04\x14\x00\x06\x00'):
                bin_file_type = "Office document (DOCX/XLSX/PPTX)"
            elif binary_data.startswith(b'\xFF\xD8\xFF'):
                bin_file_type = "JPEG image"
            elif binary_data.startswith(b'\x89PNG'):
                bin_file_type = "PNG image"
            elif binary_data.startswith(b'GIF8'):
                bin_file_type = "GIF image"
            elif binary_data.startswith(b'BM'):
                bin_file_type = "BMP image"
            
            # Build metadata analysis
            metadata_prefix = "BINARY FILE ANALYSIS:\n"
            metadata_prefix += f"Filename: {filename}\n"
            metadata_prefix += f"File Size: {file_size} bytes\n"
            metadata_prefix += f"File Signature: {file_signature}\n"
            metadata_prefix += f"Detected Type: {bin_file_type}\n"
            
            # Add insurance-specific information if found
            if has_insurance_content:
                metadata_prefix += "\nINSURANCE CONTENT DETECTED:\n"
                
                # Extract policy numbers if present
                policy_numbers = []
                for match in policy_pattern.finditer(text_sample):
                    if match and match.group(2):
                        policy_numbers.append(match.group(2))
                
                if policy_numbers:
                    metadata_prefix += f"Policy Numbers: {', '.join(policy_numbers)}\n"
                
                # Add content type indicators
                content_types = []
                if insurance_metadata["has_policy_info"]: content_types.append("Policy Information")
                if insurance_metadata["has_coverage_details"]: content_types.append("Coverage Details")
                if insurance_metadata["has_premium_info"]: content_types.append("Premium Information")
                if insurance_metadata["has_claim_info"]: content_types.append("Claim Information")
                if insurance_metadata["has_policy_document"]: content_types.append("Policy Document")
                
                if content_types:
                    metadata_prefix += f"Contains: {', '.join(content_types)}\n"
            
            # Create a safe text representation of the binary content
            # Include a sample of any readable text, hex dump, and file analysis
            
            # Add extracted text sample if it has meaningful content
            cleaned_sample = ""
            if text_sample and any(c.isalnum() for c in text_sample):
                # Filter out non-printable characters and clean up the text
                printable_text = ''.join(c if c.isprintable() else ' ' for c in text_sample)
                cleaned_sample = clean_text(printable_text)
                # Truncate if too long
                if len(cleaned_sample) > 1000:
                    cleaned_sample = cleaned_sample[:1000] + "...[truncated]"
            
            # Create hex dump of first 256 bytes for analysis
            hex_dump = ""
            hex_bytes = min(256, len(binary_data))
            for i in range(0, hex_bytes, 16):
                row_bytes = binary_data[i:i+16]
                hex_line = ' '.join([f"{b:02X}" for b in row_bytes])
                ascii_repr = ''.join(chr(b) if 32 <= b < 127 else '.' for b in row_bytes)
                hex_dump += f"{i:08X}: {hex_line.ljust(48)} | {ascii_repr}\n"
            
            # Combine metadata with analysis
            final_content = metadata_prefix + "\n\n"
            
            if cleaned_sample:
                final_content += "EXTRACTED TEXT SAMPLE:\n" + cleaned_sample + "\n\n"
            
            final_content += "HEX DUMP (First 256 bytes):\n" + hex_dump
            
            os.unlink(tmp_path)
            return [final_content]
            
        except Exception as e:
            logger.error(f"Error processing binary file: {e}")
            os.unlink(tmp_path)
            return [f"Error processing binary file {filename}: {str(e)}"]

    else:
        os.unlink(tmp_path)
        raise ValueError("Unsupported document type or unknown format.")


def get_adaptive_chunk_params(page_count):
    """Get adaptive chunking parameters based on document size.
    Using smaller overlap (10-12%) for better performance and context boundary detection.
    """
    if page_count < 100:
        return {
            'chunk_size': 800,
            'chunk_overlap': 80,  # 10% overlap (reduced from 15%)
            'min_chunk_size': 200,
            'max_chunk_size': 1000
        }
    elif page_count <= 500:
        return {
            'chunk_size': 1400,
            'chunk_overlap': 140,  # 10% overlap (reduced from 15%)
            'min_chunk_size': 300,
            'max_chunk_size': 1600
        }
    else:  # > 500 pages
        return {
            'chunk_size': 1600,
            'chunk_overlap': 192,  # 12% overlap (reduced from 15%)
            'min_chunk_size': 400,
            'max_chunk_size': 1800
        }


def process_text_for_chunking(text_batch):
    """Process a single text batch for parallel semantic chunking.
    
    Args:
        text_batch: Tuple of (batch_id, text, chunk_params)
        
    Returns:
        List of chunks for this batch
    """
    batch_id, text, chunk_params = text_batch
    sentences = re.split(r'(?<=[.!?])\s+', text)
    if len(sentences) < 2:
        return [text] if text.strip() else []

    # Calculate TF-IDF based sentence similarity - much faster than transformer models
    try:
        # Create custom stopwords list with domain-specific additions
        from sklearn.feature_extraction.text import ENGLISH_STOP_WORDS
        insurance_stop_words = list(set(ENGLISH_STOP_WORDS).union({
            'policy', 'policies', 'insurance', 'insured', 'insurer', 'insurers',
            'please', 'would', 'could', 'may', 'might', 'shall', 'should',
            'page', 'document', 'section', 'clause', 'paragraph', 'part',
            'herein', 'hereof', 'hereto', 'hereunder', 'hereby', 'thereof',
            'therefore', 'thereto', 'thereby', 'therein', 'thereon', 'therewith'
        }))

        # Use TF-IDF vectorizer for faster processing with enhanced parameters
        vectorizer = TfidfVectorizer(
            min_df=1,
            stop_words=insurance_stop_words,
            ngram_range=(1, 3),  # Use unigrams, bigrams and trigrams for better context capture
            norm='l2',
            use_idf=True,
            smooth_idf=True,
            sublinear_tf=True,  # Apply sublinear tf scaling for better handling of term frequency
            max_features=10000   # Limit vocabulary size for better performance
        )
        
        # Get sparse matrix of TF-IDF features
        tfidf_matrix = vectorizer.fit_transform(sentences)
        
        # Calculate all pairwise similarities at once for better performance
        # This avoids the loop and is much faster for large documents
        from scipy.sparse import vstack
        
        # Use scipy's efficient sparse matrix operations
        if len(sentences) > 1:
            # Get row vectors for all sentences
            rows = [tfidf_matrix.getrow(i) for i in range(tfidf_matrix.shape[0])]
            
            # Calculate consecutive similarities more efficiently
            similarities = []
            for i in range(len(rows) - 1):
                # Fast dot product for sparse matrices
                sim = rows[i].dot(rows[i+1].T).toarray()[0, 0]
                similarities.append(sim)
        else:
            similarities = []

        # Enhanced semantic boundary detection with adaptive thresholding
        if similarities:
            # Use a dynamic threshold based on document characteristics
            mean_sim = np.mean(similarities)
            std_sim = np.std(similarities)
            
            # If high variance in similarities, be more selective about boundaries
            if std_sim > 0.2:
                threshold = max(0.1, mean_sim - 1.5 * std_sim)  # More selective
            else:
                threshold = np.percentile(similarities, 20)  # Bottom 20% as boundaries
                
            # Ensure threshold isn't too high (which would create too many chunks)
            threshold = min(threshold, 0.3)
        else:
            threshold = 0.2  # Default fallback
            
        boundaries = [0]

        current_chunk_size = 0
        for i, sim in enumerate(similarities):
            current_chunk_size += len(sentences[i])

            # Create boundary if:
            # 1. Semantic similarity is low, OR
            # 2. Chunk size exceeds maximum
            if (sim < threshold and current_chunk_size > chunk_params['min_chunk_size']) or \
               current_chunk_size > chunk_params['max_chunk_size']:
                boundaries.append(i + 1)
                current_chunk_size = 0

        boundaries.append(len(sentences))

        # Create chunks with overlap
        chunks = []
        overlap_sentences = max(1, chunk_params['chunk_overlap'] // 100)  # Approximate overlap in sentences

        for i in range(len(boundaries) - 1):
            start_idx = max(0, boundaries[i] - (overlap_sentences if i > 0 else 0))
            end_idx = min(len(sentences), boundaries[i + 1] + overlap_sentences)

            chunk_text = ' '.join(sentences[start_idx:end_idx])
            if len(chunk_text.strip()) >= chunk_params['min_chunk_size']:
                chunks.append(chunk_text.strip())

        return chunks

    except Exception as e:
        logger.warning(f"Semantic chunking failed for batch {batch_id}, falling back to rule-based: {e}")
        return rule_based_chunking(text, chunk_params)

def semantic_chunking(text, chunk_params):
    """Implement parallel semantic chunking based on content similarity and structure.
    Uses TF-IDF vectorization for faster processing instead of SentenceTransformer.
    """
    # For small texts, process directly without parallelization
    if len(text) < 10000:  # Less than 10KB of text
        return process_text_for_chunking((0, text, chunk_params))
    
    # For larger texts, split into batches for parallel processing
    try:
        # Split text into roughly equal-sized batches for parallel processing
        # Use paragraph boundaries for cleaner splits
        paragraphs = re.split(r'\n\s*\n', text)
        
        # Determine optimal batch size based on text length
        total_length = len(text)
        num_paragraphs = len(paragraphs)
        
        # Determine optimal number of batches
        # Use more batches for larger texts, but cap at reasonable number
        ideal_batch_count = min(16, max(2, total_length // 20000))
        
        # Create batches with roughly equal content
        batches = []
        current_batch = []
        current_length = 0
        target_length = total_length / ideal_batch_count
        
        for para in paragraphs:
            current_batch.append(para)
            current_length += len(para) + 2  # +2 for the newlines we split on
            
            # When batch reaches target size, finalize it
            if current_length >= target_length and len(current_batch) > 0:
                batches.append('\n\n'.join(current_batch))
                current_batch = []
                current_length = 0
        
        # Add any remaining paragraphs to the last batch
        if current_batch:
            batches.append('\n\n'.join(current_batch))
        
        # Ensure we created at least one batch
        if not batches:
            batches = [text]
            
        logger.info(f"Split text into {len(batches)} batches for parallel semantic chunking")
        
        # Process batches in parallel
        batch_tasks = [(i, batch_text, chunk_params) for i, batch_text in enumerate(batches)]
        
        # Determine optimal number of workers
        # For CPU-bound tasks like this, typically n_workers = n_cpu is best
        max_workers = min(os.cpu_count() or 4, len(batch_tasks))
        
        # Process batches in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            all_batch_chunks = list(executor.map(process_text_for_chunking, batch_tasks))
        
        # Flatten the list of chunk lists
        all_chunks = []
        for batch_chunks in all_batch_chunks:
            all_chunks.extend(batch_chunks)
            
        logger.info(f"Parallel semantic chunking completed with {len(all_chunks)} chunks")
        return all_chunks
        
    except Exception as e:
        logger.warning(f"Parallel semantic chunking failed, falling back to sequential: {e}")
        # Fall back to sequential processing
        return process_text_for_chunking((0, text, chunk_params))


def rule_based_chunking(text, chunk_params):
    """Enhanced rule-based chunking with insurance-specific domain separators."""
    separators = [
        # Document structure markers (highest priority)
        "\n\n=== ", "\n\nARTICLE", "\n\nSECTION", "\n\nCLAUSE", "\n\nPART",
        "\n\nCOVERAGE", "\n\nBENEFIT", "\n\nEXCLUSION", "\n\nLIMIT",
        "\n\nArticle", "\n\nSection", "\n\nClause", "\n\nPart",
        "\n\nCoverage", "\n\nBenefit", "\n\nExclusion", "\n\nLimit",
        
        # Insurance-specific domain separators (new)
        "\n\nPOLICY ", "\n\nENDORSEMENT", "\n\nDECLARATIONS", "\n\nRIDER",
        "\n\nCONDITIONS", "\n\nDEFINITIONS", "\n\nEXCLUSIONS", "\n\nWARRANTIES",
        "\n\nSCHEDULE ", "\n\nPREMIUM ", "\n\nDEDUCTIBLE ", "\n\nCLAIMS ",
        "\n\nPolicy ", "\n\nEndorsement", "\n\nDeclarations", "\n\nRider",
        
        # Insurance sub-section separators (new)
        "\nWhat is covered:", "\nWhat is not covered:", "\nThis policy covers:", 
        "\nThis policy does not cover:", "\nLimitations:", "\nGenerally:",
        "\nIn the event of a claim:", "\nHow to claim:", "\nImportant notice:",
        
        # Insurance form-specific separators (new)
        "\nForm No.:", "\nForm Number:", "\nPolicy Number:", "\nCertificate Number:",
        "\nEffective Date:", "\nExpiration Date:", "\nRenewal Date:", "\nIssue Date:",
        
        # Table and structured data markers
        "\n[TABLE_DATA_DETECTED", "\n[KEY_VALUE_PAIRS_DETECTED",

        # Numbered sections (common in legal/insurance documents)
        r"\n\d+\.\d+", r"\n\d+\.", r"\n[A-Z]\.", r"\n[a-z]\.", r"\n[ivxIVX]+\.",
        r"\n\(\d+\)", r"\n\([a-z]\)", r"\n\([A-Z]\)",  # Common insurance subsections

        # Insurance definitions and key terms
        "\n\nDefinitions", "\n\nTerms", "\n\nGlossary", "\nMeaning of words:",
        "\nFor the purpose of this policy:", "\nFor the purposes of this Insurance:",
        
        # General document separators (lowest priority)
        "\n\n", "\n", ". ", "; ", ", ", " "
    ]

    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_params['chunk_size'],
        chunk_overlap=chunk_params['chunk_overlap'],
        separators=separators,
        length_function=len
    )

    return splitter.split_text(text)


def process_page_parallel(args):
    """Process a single page in parallel
    
    Args:
        args: Tuple containing (page_num, page_text, chunk_params)
        
    Returns:
        list: List of chunk dictionaries for this page
    """
    page_num, page_text, chunk_params = args
    
    if not page_text.strip():
        return []

    # Apply enhanced preprocessing
    processed_text = enhanced_text_preprocessing(page_text)

    if not processed_text.strip():
        return []

    # Use semantic chunking for better content boundaries
    page_chunks = semantic_chunking(processed_text, chunk_params)
    
    chunks = []
    for i, chunk in enumerate(page_chunks):
        if len(chunk.strip()) < chunk_params['min_chunk_size']:
            continue
            
        # Rest of processing for this chunk
        # Enhanced context prefix with better identification
        first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
        first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]

        # Detect chunk type for better context
        chunk_type = "content"
        if "[TABLE_DATA_DETECTED" in chunk:
            chunk_type = "table"
        elif "[KEY_VALUE_PAIRS_DETECTED" in chunk:
            chunk_type = "structured_data"
        elif "===" in chunk:
            chunk_type = "section_header"

        context_prefix = f"Page {page_num}, {chunk_type.title()} {i+1}: {first_sentence}... "

        # Enhanced pattern detection with better accuracy
        chunk_lower = chunk.lower()
        contains_definition = 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower))
        contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower))
        contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower))
        contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower))
        contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower))
        contains_tabular = "[TABLE_DATA_DETECTED" in chunk or "|" in chunk
        contains_numeric = bool(REGEX_PATTERNS['numeric_data'].search(chunk))

        metadata = []
        if contains_definition: metadata.append("definition")
        if contains_exclusion: metadata.append("exclusion")
        if contains_coverage: metadata.append("coverage")
        if contains_limit: metadata.append("limit")
        if contains_condition: metadata.append("condition")
        if contains_tabular: metadata.append("tabular_data")
        if contains_numeric: metadata.append("numeric_data")
        metadata.append(chunk_type)

        chunks.append({
            "text": context_prefix + chunk,
            "page": page_num,
            "section": i+1,
            "raw_text": chunk,
            "metadata": metadata,
            "chunk_size": len(chunk),
            "chunk_type": chunk_type
        })
        
    return chunks


def generate_smart_chunks(text_by_page):
    """Enhanced chunking with adaptive sizing, semantic awareness, and parallel processing."""
    page_count = len(text_by_page)
    chunk_params = get_adaptive_chunk_params(page_count)

    logger.info(f"Using adaptive chunking for {page_count} pages: {chunk_params}")

    # Prepare for parallel processing
    tasks = [(page_num, page_text, chunk_params) 
             for page_num, page_text in enumerate(text_by_page, 1) 
             if page_text.strip()]
             
    # Use ThreadPoolExecutor for parallel processing
    # Determine optimal worker count - max 16 workers, min(CPU cores, page_count)
    max_workers = min(16, os.cpu_count() or 4, len(tasks))
    
    if max_workers > 1 and len(tasks) > 1:
        logger.info(f"Processing {len(tasks)} pages in parallel with {max_workers} workers")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            all_chunks = list(executor.map(process_page_parallel, tasks))
        
        # Flatten the list of lists
        chunks = [chunk for page_chunks in all_chunks for chunk in page_chunks]
    else:
        # Fallback to sequential processing for small documents
        chunks = []
        for page_num, page_text in enumerate(text_by_page, 1):
            if not page_text.strip():
                continue
                
            # Apply enhanced preprocessing
            processed_text = enhanced_text_preprocessing(page_text)

            if not processed_text.strip():
                continue

            # Use semantic chunking for better content boundaries
            page_chunks = semantic_chunking(processed_text, chunk_params)

            for i, chunk in enumerate(page_chunks):
                if len(chunk.strip()) < chunk_params['min_chunk_size']:
                    continue

                # Detect chunk type for better context
                chunk_type = "content"
                if "[TABLE_DATA_DETECTED" in chunk:
                    chunk_type = "table"
                elif "[KEY_VALUE_PAIRS_DETECTED" in chunk:
                    chunk_type = "structured_data"
                elif "===" in chunk:
                    chunk_type = "section_header"
                
                # Enhanced context prefix with better identification
                first_sentence_match = REGEX_PATTERNS['first_sentence'].split(chunk.strip())
                first_sentence = first_sentence_match[0][:100] if first_sentence_match else chunk.strip()[:100]
                
                context_prefix = f"Page {page_num}, {chunk_type.title()} {i+1}: {first_sentence}... "

                # Enhanced pattern detection with better accuracy
                chunk_lower = chunk.lower()
                contains_definition = 'means' in chunk_lower and bool(re.search(r'\b\w+\s+means\b', chunk_lower))
                contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk_lower))
                contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk_lower))
                contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk_lower))
                contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk_lower))
                contains_tabular = "[TABLE_DATA_DETECTED" in chunk or "|" in chunk
                contains_numeric = bool(REGEX_PATTERNS['numeric_data'].search(chunk))

                metadata = []
                if contains_definition: metadata.append("definition")
                if contains_exclusion: metadata.append("exclusion")
                if contains_coverage: metadata.append("coverage")
                if contains_limit: metadata.append("limit")
                if contains_condition: metadata.append("condition")
                if contains_tabular: metadata.append("tabular_data")
                if contains_numeric: metadata.append("numeric_data")
                metadata.append(chunk_type)

                chunks.append({
                    "text": context_prefix + chunk,
                    "page": page_num,
                    "section": i+1,
                    "raw_text": chunk,
                    "metadata": metadata,
                    "chunk_size": len(chunk),
                    "chunk_type": chunk_type
                })

    logger.info(f"Generated {len(chunks)} chunks with adaptive sizing")
    return chunks


def prepare_text_for_embedding(chunk):
    """Prepare a single chunk's text for embedding with enhanced metadata context
    
    Args:
        chunk: A single chunk dictionary
        
    Returns:
        str: The prepared text with metadata prefixes
    """
    text = chunk["text"]
    metadata = chunk.get("metadata", [])
    chunk_type = chunk.get("chunk_type", "content")

    # Add metadata context for better embeddings
    if "tabular_data" in metadata:
        text = f"[TABULAR_DATA] {text}"
    if "definition" in metadata:
        text = f"[DEFINITION] {text}"
    if "numeric_data" in metadata:
        text = f"[NUMERIC_DATA] {text}"
    if chunk_type == "section_header":
        text = f"[SECTION_HEADER] {text}"
        
    return text


def embed_chunks_openai(chunks):
    """Enhanced embedding with quality improvements, metadata awareness, and parallelism."""
    # Process texts in parallel for large datasets
    if len(chunks) > 100:
        # Determine optimal worker count for text preparation (CPU-bound)
        max_workers = min(32, (os.cpu_count() or 4) * 2, len(chunks))
        
        logger.info(f"Preparing {len(chunks)} chunk texts in parallel with {max_workers} workers")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            texts = list(executor.map(prepare_text_for_embedding, chunks))
    else:
        # For smaller datasets, process sequentially
        texts = []
        # Prepare texts with enhanced context based on metadata
        for chunk in chunks:
            texts.append(prepare_text_for_embedding(chunk))

    try:
        # Process in batches to avoid API limits and improve efficiency
        batch_size = 50  # Reduced batch size for better quality
        all_embeddings = []

        logger.info(f"Generating embeddings for {len(texts)} chunks in batches of {batch_size}")

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            # Use text-embedding-3-large for maximum accuracy (user preference)
            resp = client.embeddings.create(
                model="text-embedding-3-large",
                input=batch_texts,
                dimensions=3072  # Full dimensionality for maximum accuracy
            )
            batch_embeddings = [d.embedding for d in resp.data]
            all_embeddings.extend(batch_embeddings)

            logger.debug(f"Processed batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")

        embeddings = np.array(all_embeddings, dtype=np.float32)

        # Enhanced normalization for better similarity computation
        norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
        norms[norms == 0] = 1  # Avoid division by zero
        norm_embeddings = embeddings / norms

        # Build enhanced FAISS index with better performance
        dimension = norm_embeddings.shape[1]

        # Use IndexHNSWFlat for better performance on larger datasets
        if len(chunks) > 1000:
            index = faiss.IndexHNSWFlat(dimension, 32)  # 32 connections per node
            index.hnsw.efConstruction = 200  # Higher quality construction
            index.hnsw.efSearch = 50  # Better search quality
        else:
            # Use IndexFlatIP for smaller datasets (exact search)
            index = faiss.IndexFlatIP(dimension)

        index.add(norm_embeddings)

        logger.info(f"Created FAISS index with {index.ntotal} vectors")
        return index, chunks, norm_embeddings

    except Exception as e:
        logger.error(f"Embedding generation failed: {e}")
        raise e


def expand_query(question):
    """Generate expanded queries for better retrieval
    
    This function creates variations of the original query to improve retrieval by:
    1. Adding insurance-specific terminology
    2. Creating multiple phrasings of complex questions
    3. Adding domain-specific synonyms
    
    Args:
        question: The original user question
        
    Returns:
        list: A list of expanded query variations including the original
    """
    # Always keep the original query
    expanded_queries = [question]
    question_lower = question.lower()
    
    # Insurance-specific term mapping for expansion
    insurance_term_map = {
        "pay": ["premium", "payment", "cost", "fee"],
        "cost": ["premium", "price", "charge", "fee"],
        "rules": ["terms", "conditions", "provisions", "stipulations"],
        "not covered": ["excluded", "exclusion", "not eligible", "not included"],
        "covered": ["included", "eligible", "protection for", "coverage for"],
        "family": ["dependents", "spouse and children", "household members"],
        "start": ["effective date", "commencement", "inception date"],
        "end": ["expiry", "termination", "expiration date"],
        "document": ["policy", "certificate", "contract"],
        "money back": ["refund", "reimbursement", "premium return"]
    }
    
    # Simple query enhancement with domain terms
    for term, replacements in insurance_term_map.items():
        if term in question_lower:
            # Create a variation with each alternative term
            for replacement in replacements:
                if replacement not in question_lower:  # Avoid duplicating existing terms
                    new_query = question.lower().replace(term, replacement)
                    if new_query != question_lower:
                        expanded_queries.append(new_query)
    
    # Generate question variations for common insurance question patterns
    if "what" in question_lower and "cover" in question_lower:
        expanded_queries.append(f"what is included in {question_lower.split('what')[1]}")
        expanded_queries.append(f"what protection is provided for {question_lower.split('cover')[1]}")
    
    if "how much" in question_lower:
        if "cost" in question_lower:
            expanded_queries.append(question_lower.replace("how much", "what is the premium for"))
        if "pay" in question_lower:
            expanded_queries.append(question_lower.replace("how much", "what is the amount"))
    
    if "how to" in question_lower and "claim" in question_lower:
        expanded_queries.append("claim process procedure")
        expanded_queries.append(f"steps to submit a claim for {question_lower.split('claim')[1]}")
    
    # Limit to reasonable number of expanded queries
    # More than 3 can slow down processing significantly
    expanded_queries = expanded_queries[:3]
    
    return expanded_queries


def insurance_specific_retrieve(question, index, chunks, k=6):
    """Enhanced retrieval with improved question analysis and metadata awareness."""
    # Enhanced query preprocessing
    question_lower = question.lower()
    
    # Generate expanded queries for better retrieval
    expanded_queries = expand_query(question)

    # Enhanced question type classification with more categories
    question_type = "general"
    question_categories = []
    
    # Check for tabular data questions
    if any(word in question_lower for word in ["table", "chart", "data", "amount", "number", "cost", "price", "list", "figures"]):
        question_type = "tabular"
        question_categories.append("tabular")
        question = f"[TABULAR_DATA] {question}"
    
    # Check for definition questions
    if any(word in question_lower for word in ["define", "definition", "what is", "what are", "meaning", "explain", "term"]):
        question_type = "definition"
        question_categories.append("definition")
        question = f"[DEFINITION] {question}"
    
    # Check for structural questions
    if any(word in question_lower for word in ["section", "article", "clause", "part", "chapter", "paragraph"]):
        question_type = "structural"
        question_categories.append("structural")
        question = f"[SECTION_HEADER] {question}"
    
    # Check for coverage questions
    if any(word in question_lower for word in ["cover", "coverage", "includes", "covered", "protect", "insurance for"]):
        question_categories.append("coverage")
        question = f"[COVERAGE] {question}"
    
    # Check for exclusion questions
    if any(word in question_lower for word in ["exclude", "exclusion", "not covered", "exempt", "limitation"]):
        question_categories.append("exclusion")
        question = f"[EXCLUSION] {question}"
    
    # Check for claim questions
    if any(word in question_lower for word in ["claim", "claims", "filing", "process", "procedure", "submit"]):
        question_categories.append("claims")
        question = f"[CLAIMS] {question}"
    
    # Check for premium/payment questions
    if any(word in question_lower for word in ["premium", "cost", "price", "payment", "fee", "charge"]):
        question_categories.append("payment")
        question = f"[PAYMENT] {question}"

    # Use query expansion to get multiple query vectors
    try:
        # Get embeddings for all expanded queries using large model for accuracy
        q_resp = client.embeddings.create(
            model="text-embedding-3-large", 
            input=expanded_queries,
            dimensions=3072  # Full dimensionality for maximum accuracy
        )
        
        # Process all query vectors
        q_vecs = []
        for embedding_data in q_resp.data:
            vec = np.array(embedding_data.embedding, dtype=np.float32)
            # Normalize the vector
            vec = vec / np.linalg.norm(vec)
            q_vecs.append(vec)
            
        # Primary query vector (from original question)
        q_vec = q_vecs[0]
        
    except Exception as e:
        logger.error(f"Query embedding failed: {e}")
        raise e

    # Get more candidates first (adaptive based on question categories for better filtering)
    # Default multiplier
    search_multiplier = 4
    
    # Adjust multiplier based on question categories
    if 'tabular' in question_categories:
        search_multiplier = 6  # Need more candidates for tabular data to ensure we get the right table
    elif len(question_categories) > 1:
        search_multiplier = 5  # More candidates for multi-category questions
    elif question_type == 'definition':
        search_multiplier = 3  # Definitions are usually precise and need fewer candidates
        
    # Combine results from all query variations
    all_indices = set()
    all_scores = {}
    
    # Search with each query vector
    for i, vec in enumerate(q_vecs):
        # Weight original query higher than expanded queries
        weight = 1.0 if i == 0 else 0.7
        
        # Search using this query variation
        scores_i, indices_i = index.search(np.array([vec]), k * search_multiplier)
        
        # Add results to combined set with weights
        for rank, idx in enumerate(indices_i[0]):
            idx_int = int(idx)
            all_indices.add(idx_int)
            
            # Apply rank-based scoring with query-specific weight
            score = scores_i[0][rank] * weight * (1.0 - (rank / (k * search_multiplier * 2)))
            
            # Combine scores (take max score if already exists)
            if idx_int in all_scores:
                all_scores[idx_int] = max(all_scores[idx_int], score)
            else:
                all_scores[idx_int] = score
    
    # Convert back to arrays for processing
    indices = list(all_indices)
    scores = [all_scores[idx] for idx in indices]
    
    # Create format compatible with rest of function
    indices = [indices]
    scores = [scores]
    
    logger.info(f"Query expansion: combined {len(expanded_queries)} queries into {len(indices[0])} unique results")

    # Pre-defined insurance keywords set for faster lookup
    insurance_keywords = {
        'claim', 'premium', 'coverage', 'deductible', 'exclusion',
        'benefit', 'policy', 'insured', 'limit', 'condition', 'amount',
        'liability', 'copay', 'coinsurance', 'network', 'provider',
        'reimbursement', 'payment', 'cost', 'fee', 'charge', 'expense',
        'maximum', 'minimum', 'percentage', 'dollar', 'annual', 'monthly',
        'eligible', 'eligibility', 'waiting', 'period', 'effective', 'date',
        'termination', 'renewal', 'grace', 'notification', 'approval', 'document',
        'required', 'submission', 'proof', 'evidence', 'terms', 'clause',
        'diagnosis', 'treatment', 'procedure', 'physician', 'hospital',
        'prescription', 'medication', 'emergency', 'preventive', 'specialist',
        'definition', 'scope', 'coverage', 'exclusion', 'exception', 'provision',
        'endorsement', 'schedule', 'attachment', 'addendum'
    }

    # Extract key terms from question (optimized)
    question_lower = question.lower()
    question_words = set(re.findall(r"\w+", question_lower))
    question_words_list = list(question_words)
    question_bigrams = set([' '.join(pair) for pair in zip(question_words_list, question_words_list[1:])])
    
    # Pre-compute question characteristics for efficiency
    question_has_numbers = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit', question_lower))
    is_definition_question = any(word in question_lower for word in ['what is', 'define', 'meaning'])

    top_matches = []
    for rank, i in enumerate(indices[0]):
        chunk = chunks[i]
        raw_text = chunk["raw_text"]
        text_lower = raw_text.lower()

        # Optimized keyword extraction and matching
        text_words = set(re.findall(r"\w+", text_lower))
        common_keywords = question_words & text_words
        insurance_terms = common_keywords & insurance_keywords

        # Check for exact phrase matches (optimized)
        phrase_matches = sum(1 for word in question_words
                           if len(word) > 3 and word in text_lower)

        # Check for bigram matches (optimized)
        bigram_matches = sum(3 for bigram in question_bigrams if bigram in text_lower)

        # Check for numerical/financial content matches
        has_numbers = bool(re.search(r'\$|%|\d+', raw_text))
        number_bonus = 0.15 if (has_numbers and question_has_numbers) else 0

        # Look for definition patterns ("X means Y")
        definition_pattern = 'means' in text_lower and bool(re.search(r'\b\w+\s+means\b', text_lower))
        definition_bonus = 0.2 if (definition_pattern and is_definition_question) else 0

        # Enhanced metadata-based scoring
        metadata = chunk.get("metadata", [])
        chunk_type = chunk.get("chunk_type", "content")

        # Metadata bonuses based on question type
        metadata_bonus = 0
        if question_type == "tabular" and "tabular_data" in metadata:
            metadata_bonus += 0.3
        elif question_type == "definition" and "definition" in metadata:
            metadata_bonus += 0.25
        elif question_type == "structural" and chunk_type == "section_header":
            metadata_bonus += 0.2

        # Additional bonuses for relevant metadata
        if "numeric_data" in metadata and question_has_numbers:
            metadata_bonus += 0.15
        if any(meta in metadata for meta in ["coverage", "exclusion", "limit", "condition"]):
            metadata_bonus += 0.1

        keyword_score = len(common_keywords) + (len(insurance_terms) * 2) + phrase_matches + bigram_matches
        semantic_score = scores[0][rank]

        # Hybrid search scoring with optimized weights 
        # Increase semantic weight to 0.5 (from 0.6) and keyword weight to 0.25 (from 0.15)
        # This creates a better balance between semantic and lexical matching
        final_score = (0.5 * semantic_score) + \
                      (0.25 * min(keyword_score / 10.0, 1.0)) + \
                      number_bonus + \
                      definition_bonus + \
                      metadata_bonus

        top_matches.append({
            "text": chunk["text"],
            "raw_text": raw_text,
            "page": chunk["page"],
            "score": final_score,
            "metadata": metadata,
            "chunk_type": chunk_type,
            "semantic_score": semantic_score,
            "keyword_score": keyword_score
        })

    # Sort by score first
    top_matches = sorted(top_matches, key=lambda x: x["score"], reverse=True)
    
    # Apply Maximum Marginal Relevance to reduce redundancy
    # Lambda parameter controls diversity (0=max diversity, 1=max relevance)
    lambda_param = 0.7  # Balance between relevance and diversity
    
    # Start with the highest scoring result
    mmr_selected = [top_matches[0]] if top_matches else []
    remaining = top_matches[1:] if top_matches else []
    
    # Calculate similarity between chunks based on content
    def content_similarity(chunk1, chunk2):
        # Simple text overlap heuristic - percentage of words in common
        words1 = set(chunk1['raw_text'].lower().split())
        words2 = set(chunk2['raw_text'].lower().split())
        
        if not words1 or not words2:
            return 0
            
        # Jaccard similarity
        overlap = len(words1.intersection(words2))
        similarity = overlap / len(words1.union(words2))
        return similarity
    
    # Select remaining chunks using MMR
    while len(mmr_selected) < k and remaining:
        max_score = -float('inf')
        max_idx = -1
        
        for i, chunk in enumerate(remaining):
            # Original relevance score
            relevance = chunk['score']
            
            # Calculate max similarity to already selected chunks
            max_sim = max([content_similarity(chunk, selected) for selected in mmr_selected], default=0)
            
            # MMR score
            mmr_score = lambda_param * relevance - (1 - lambda_param) * max_sim
            
            if mmr_score > max_score:
                max_score = mmr_score
                max_idx = i
                
        if max_idx != -1:  # Found a chunk to add
            mmr_selected.append(remaining[max_idx])
            remaining.pop(max_idx)
        else:
            break
    
    # Log MMR impact
    if len(top_matches) > k:
        logger.debug(f"MMR reduced redundancy: selected {len(mmr_selected)} chunks from {len(top_matches)} candidates")
    
    return mmr_selected


def validate_context_relevance(question, context_chunks, min_relevance=0.25):
    """Enhanced filtering of context chunks based on semantic relevance and metadata."""
    question_words = set(re.findall(r'\w+', question.lower()))
    question_lower = question.lower()

    # Enhanced question type identification
    is_definition_question = bool(re.search(r'\bwhat is|\bdefine|\bmeaning|\bdefin[ei]|\bconcept', question_lower))
    is_coverage_question = bool(re.search(r'\bcover|\bprovide|\binclude|\beligible', question_lower))
    is_exclusion_question = bool(re.search(r'\bexclude|\bnot cover|\bdeny|\breject', question_lower))
    is_process_question = bool(re.search(r'\bhow|\bprocess|\bprocedure|\bsteps|\bsubmit', question_lower))
    is_document_question = bool(re.search(r'\bdocument|\bproof|\bevidence|\breceipt|\bform', question_lower))
    is_limit_question = bool(re.search(r'\blimit|\bmaximum|\bminimum|\bcap|\bceiling|\bamount', question_lower))
    is_tabular_question = bool(re.search(r'\btable|\bchart|\bdata|\bamount|\bnumber|\bcost|\bprice|\blist', question_lower))
    is_numeric_question = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit|number|value', question_lower))
    
    relevant_chunks = []
    for chunk in context_chunks:
        chunk_words = set(re.findall(r'\w+', chunk['raw_text'].lower()))
        chunk_text = chunk['raw_text'].lower()

        # Basic overlap score
        overlap = len(question_words & chunk_words)
        relevance = overlap / max(len(question_words), 1)

        # Enhanced metadata-based scoring
        metadata = chunk.get('metadata', [])
        chunk_type = chunk.get('chunk_type', 'content')

        # Enhanced type bonuses with metadata awareness
        type_bonus = 0
        if is_definition_question and ('definition' in metadata or bool(re.search(r'\bmeans\b|\bis defined as\b', chunk_text))):
            type_bonus += 0.35
        if is_coverage_question and ('coverage' in metadata or bool(re.search(r'\bcovered\b|\beligible\b', chunk_text))):
            type_bonus += 0.35
        if is_exclusion_question and ('exclusion' in metadata or bool(re.search(r'\bexcluded\b|\bnot covered\b', chunk_text))):
            type_bonus += 0.35
        if is_process_question and bool(re.search(r'\bsteps\b|\bprocess\b|\bprocedure\b', chunk_text)):
            type_bonus += 0.3
        if is_document_question and bool(re.search(r'\bdocument\b|\bproof\b|\bevidence\b|\bform\b', chunk_text)):
            type_bonus += 0.3
        if is_limit_question and ('limit' in metadata or bool(re.search(r'\blimit\b|\bmaximum\b|\bcap\b', chunk_text))):
            type_bonus += 0.35
        if is_tabular_question and ('tabular_data' in metadata or chunk_type == 'table'):
            type_bonus += 0.4  # Higher bonus for tabular questions
        if is_numeric_question and ('numeric_data' in metadata or bool(re.search(r'\$|%|\d+', chunk_text))):
            type_bonus += 0.25

        # Additional bonuses for structured content
        if chunk_type == 'section_header' and any(word in question_lower for word in ['section', 'article', 'part']):
            type_bonus += 0.3
        if 'structured_data' in chunk_type and any(word in question_lower for word in ['list', 'items', 'details']):
            type_bonus += 0.2

        # Final relevance score with enhanced bonus
        final_relevance = relevance + type_bonus

        # More lenient threshold for high-quality chunks
        min_threshold = min_relevance * 0.8 if type_bonus > 0.2 else min_relevance

        if final_relevance >= min_threshold or len(relevant_chunks) < 3:  # Keep at least 3 chunks
            relevant_chunks.append({
                **chunk,
                "relevance_score": final_relevance,
                "type_bonus": type_bonus
            })

    # Sort by relevance score and limit to top chunks
    relevant_chunks = sorted(relevant_chunks, key=lambda x: x.get("relevance_score", 0), reverse=True)

    # Adaptive chunk count based on question complexity
    max_chunks = 10 if is_tabular_question or is_numeric_question else 8
    return relevant_chunks[:max_chunks]


def build_insurance_prompt(question, context_chunks):
    """
    Enhanced prompt builder with context type awareness and improved instructions.

    Parameters:
        question (str): The user's question.
        context_chunks (list of dict): List of context dictionaries with enhanced metadata.

    Returns:
        str: The formatted prompt to send to the LLM.
    """
    # Analyze context types for better prompt customization
    has_tabular = any('tabular_data' in chunk.get('metadata', []) for chunk in context_chunks)
    has_definitions = any('definition' in chunk.get('metadata', []) for chunk in context_chunks)
    has_numeric = any('numeric_data' in chunk.get('metadata', []) for chunk in context_chunks)

    # Build context with type indicators
    context_parts = []
    for i, chunk in enumerate(context_chunks, 1):
        chunk_type = chunk.get('chunk_type', 'content')
        metadata = chunk.get('metadata', [])

        # Add type indicator for better LLM understanding
        type_indicator = ""
        if 'tabular_data' in metadata:
            type_indicator = "[TABLE DATA] "
        elif 'definition' in metadata:
            type_indicator = "[DEFINITION] "
        elif chunk_type == 'section_header':
            type_indicator = "[SECTION HEADER] "
        elif 'numeric_data' in metadata:
            type_indicator = "[NUMERIC DATA] "

        context_parts.append(f"Context {i}: {type_indicator}{chunk['text']}")

    context = "\n---\n".join(context_parts)

    # Customize instructions based on context type
    special_instructions = ""
    if has_tabular:
        special_instructions += "\n- When referencing tabular data, preserve exact numbers and structure"
        special_instructions += "\n- For tables, clearly state the specific values and their relationships"
    if has_definitions:
        special_instructions += "\n- For definitions, provide the exact definition as stated in the document"
    if has_numeric:
        special_instructions += "\n- Include specific numbers, amounts, and percentages when relevant"

    return f"""
You are a helpful insurance assistant explaining policy details in simple terms. Answer questions about insurance policies in a friendly, conversational tone.

*CRITICAL REQUIREMENTS:*
- Keep responses short and concise (1-2 lines whenever possible)
- Use plain, everyday language instead of technical insurance jargon
- Include only the most essential information like key numbers, conditions, and limits
- Be direct and get straight to the point
- Never add information not found in the context{special_instructions}
- Use proper line breaks (\\n) to separate different points or sections for better readability
- For lists or multiple items, use line breaks to separate each item

*RESPONSE STYLE EXAMPLES:*
- "An accident is any sudden, unexpected event caused by something external and visible."
- "Children up to 23 years old can be covered if they depend financially on you."
- "If you're permanently disabled, you'll get 100% of your insured amount."
- "The premium for this coverage is $150 per month with a $500 deductible."
- For multiple items: "Coverage includes:\\n- Medical expenses\\n- Disability benefits\\n- Death benefits"

*ENHANCED CONTEXT FROM POLICY DOCUMENT:*
{context}

*QUESTION:* {question}

Provide a short, friendly answer using simple language. If the context contains tables or specific data, include relevant details. Use \\n for line breaks when listing multiple items or separating different concepts. Respond in JSON format:
{{ "answer": "..." }}
"""


def format_response_text(text):
    """
    Format response text to handle newlines and improve readability.
    Converts literal \n to actual newlines and cleans up formatting.
    """
    if not text:
        return text

    # Replace literal \n with actual newlines
    text = text.replace('\\n', '\n')

    # Also handle other common escape sequences
    text = text.replace('\\t', '\t')  # Handle tabs
    text = text.replace('\\"', '"')   # Handle escaped quotes

    # Clean up excessive newlines (more than 2 consecutive)
    text = re.sub(r'\n{3,}', '\n\n', text)

    # Remove trailing/leading whitespace while preserving internal formatting
    text = text.strip()

    # Ensure proper spacing after periods and colons (but not in numbers)
    text = re.sub(r'\.(?=[A-Z])', '. ', text)
    text = re.sub(r':(?=[A-Z])', ': ', text)

    # Fix spacing around bullet points and dashes
    text = re.sub(r'\n-\s*', '\n- ', text)
    text = re.sub(r'\n•\s*', '\n• ', text)
    text = re.sub(r'\n\*\s*', '\n* ', text)

    # Ensure proper spacing after numbered lists
    text = re.sub(r'\n(\d+)\.\s*', r'\n\1. ', text)

    # Clean up extra spaces
    text = re.sub(r' {2,}', ' ', text)

    # Ensure sentences end with proper punctuation spacing
    text = re.sub(r'([.!?])([A-Z])', r'\1 \2', text)

    return text


def determine_question_type(question):
    """Analyze question to determine if it's factual or explanatory
    
    Args:
        question: The question text
    
    Returns:
        str: "factual" or "explanatory"
    """
    question_lower = question.lower()
    
    # Patterns indicating explanatory questions
    explanatory_patterns = [
        "explain", "why", "how", "what is the reason", "what are the reasons",
        "describe", "elaborate", "clarify", "what happens", "tell me about",
        "difference between", "compare", "contrast", "what does it mean"
    ]
    
    # Check for explanatory patterns
    if any(pattern in question_lower for pattern in explanatory_patterns):
        return "explanatory"
    
    # Default to factual for direct questions
    return "factual"


def is_insurance_related_query(question):
    """Determine if a question is related to insurance or policy domains using semantic analysis.

    Uses GPT-4o mini to analyze the context and meaning of the question to accurately classify
    whether it's insurance/policy related or not.

    Args:
        question: The user's question text

    Returns:
        bool: True if the question appears to be insurance/policy related
    """
    try:
        # Create a focused prompt for domain classification
        classification_prompt = f"""You are an expert classifier that determines if questions are related to insurance, policies, or financial coverage.

Analyze this question and determine if it's related to insurance/policy domains:

Question: "{question}"

Insurance/Policy related topics include:
- Health, life, auto, home, property, travel insurance
- Policy coverage, benefits, exclusions, limits
- Premiums, deductibles, copays, claims
- Policy renewals, cancellations, endorsements
- Beneficiaries, dependents, providers
- Insurance companies, agents, brokers
- Coverage verification, claim filing
- Policy documents, certificates, declarations

NON-insurance topics include:
- Mathematics, calculations, arithmetic
- Weather, geography, general knowledge
- Entertainment, sports, music, movies
- Cooking, recipes, food
- Technology, programming, computers
- General "how-to" questions unrelated to insurance
- Time, dates, schedules (unless policy-related)

Respond with ONLY one word:
- "INSURANCE" if the question is about insurance/policy topics
- "NON-INSURANCE" if the question is about other topics

Classification:"""

        # Use GPT-4o mini for classification
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "user",
                    "content": classification_prompt
                }
            ],
            max_tokens=10,  # We only need one word response
            temperature=0.1,  # Low temperature for consistent classification
            timeout=10  # Quick timeout for fast classification
        )

        classification = response.choices[0].message.content.strip().upper()

        # Log the classification for debugging
        logger.info(f"Question: '{question[:50]}...' -> Classification: {classification}")

        # Return True if classified as insurance-related
        return classification == "INSURANCE"

    except Exception as e:
        logger.error(f"Error in semantic classification for question '{question[:50]}...': {e}")

        # Fallback to simple keyword-based classification if API fails
        logger.info("Falling back to keyword-based classification")
        return fallback_keyword_classification(question)


def fallback_keyword_classification(question):
    """Fallback keyword-based classification when semantic analysis fails.

    Args:
        question: The user's question text

    Returns:
        bool: True if the question appears to be insurance/policy related
    """
    # Simple keyword-based fallback
    insurance_keywords = [
        'insurance', 'policy', 'coverage', 'premium', 'claim', 'deductible',
        'beneficiary', 'copay', 'coinsurance', 'exclusion', 'liability'
    ]

    question_lower = question.lower()

    # Quick exclusion of obvious math questions
    if re.search(r'\d+\s*[\+\-\*\/]\s*\d+', question):
        return False

    # Check for insurance keywords
    for keyword in insurance_keywords:
        if re.search(r'\b' + re.escape(keyword) + r'\b', question_lower):
            return True

    return False


def extract_text_from_image_shape(shape, slide_number):
    """Extract text from image shapes using OCR with error handling and optimization."""
    if not OCR_AVAILABLE:
        return None

    try:
        # Method 1: Try to get image from shape.image
        image_data = None
        if hasattr(shape, 'image') and shape.image is not None:
            try:
                image_data = shape.image.blob
            except Exception as e:
                logger.debug(f"Failed to get image blob from shape.image: {e}")

        # Method 2: Try alternative image extraction methods
        if not image_data:
            # Try to get image from shape parts
            try:
                if hasattr(shape, '_element') and hasattr(shape._element, 'xpath'):
                    # Look for embedded images in the shape XML
                    blip_elements = shape._element.xpath('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'})
                    if blip_elements:
                        # Try to extract image from the first blip element
                        embed_id = blip_elements[0].get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed_id and hasattr(shape, 'part') and hasattr(shape.part, 'related_parts'):
                            related_part = shape.part.related_parts.get(embed_id)
                            if related_part and hasattr(related_part, 'blob'):
                                image_data = related_part.blob
                                logger.debug(f"Extracted image data from related part for slide {slide_number}")
            except Exception as e:
                logger.debug(f"Alternative image extraction failed: {e}")

        if not image_data:
            return None

        # Convert to PIL Image
        import io
        image_stream = io.BytesIO(image_data)
        pil_image = PILImage.open(image_stream)

        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # Get image dimensions for processing decisions
        width, height = pil_image.size
        image_area = width * height
        logger.debug(f"Processing image shape ({width}x{height}) on slide {slide_number}")

        # Enhanced image preprocessing for better OCR results
        import numpy as np

        # Convert PIL to OpenCV format
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # Choose processing approach based on image characteristics
        extracted_texts = []

        # For smaller images or shapes, use gentle processing
        if image_area < 500000:  # Small to medium images
            try:
                # Minimal processing for small, clear images
                gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
                
                # Light denoising
                denoised = cv2.fastNlMeansDenoising(gray, h=5)
                
                # Convert back to PIL for tesseract
                processed_pil = PILImage.fromarray(denoised)
                
                # Configure tesseract for small images
                custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` "'
                
                extracted_text = pytesseract.image_to_string(processed_pil, config=custom_config)
                
                if extracted_text:
                    extracted_texts.append(("gentle_processing", extracted_text))
                    
            except Exception as e:
                logger.debug(f"Gentle OCR processing failed: {e}")

        # For larger images, use more aggressive processing
        else:
            try:
                # Convert to grayscale
                gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)

                # Apply stronger denoising for larger images
                denoised = cv2.fastNlMeansDenoising(gray, h=10)

                # Apply adaptive thresholding for better text contrast
                thresh = cv2.adaptiveThreshold(
                    denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 2
                )

                # Apply morphological operations to clean up the image
                kernel = np.ones((2, 2), np.uint8)
                processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

                # Convert back to PIL for tesseract
                processed_pil = PILImage.fromarray(processed)

                # Configure tesseract for better accuracy on processed images
                custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` "'

                # Extract text using tesseract
                extracted_text = pytesseract.image_to_string(processed_pil, config=custom_config)
                
                if extracted_text:
                    extracted_texts.append(("aggressive_processing", extracted_text))

            except Exception as e:
                logger.debug(f"Aggressive OCR processing failed: {e}")

        # Try scaling if initial attempts don't yield good results
        if not extracted_texts or all(len(text[1].strip()) < 10 for text in extracted_texts):
            try:
                # Scale up the image for better OCR on small text
                scale_factor = 2.0 if image_area < 300000 else 1.5
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                
                # Resize with high-quality interpolation
                resized = cv2.resize(opencv_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                gray_scaled = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
                
                processed_pil = PILImage.fromarray(gray_scaled)
                custom_config = r'--oem 3 --psm 6'
                
                scaled_text = pytesseract.image_to_string(processed_pil, config=custom_config)
                
                if scaled_text:
                    extracted_texts.append((f"scaled_{scale_factor}", scaled_text))
                    
            except Exception as e:
                logger.debug(f"Scaling OCR failed: {e}")

        # Select the best extraction result
        best_text = None
        if extracted_texts:
            # Choose the extraction with the most meaningful content
            valid_extractions = [(method, text) for method, text in extracted_texts if len(text.strip()) >= 3]
            
            if valid_extractions:
                # Prefer longer, more meaningful text
                best_extraction = max(valid_extractions, key=lambda x: len(x[1]))
                best_text = best_extraction[1]
                logger.debug(f"Best OCR result from {best_extraction[0]} method: {len(best_text)} characters")

        # Clean and validate extracted text
        if best_text:
            # Remove excessive whitespace and clean up
            cleaned_text = ' '.join(best_text.split())

            # Filter out very short or nonsensical extractions
            if len(cleaned_text) >= 3 and any(c.isalnum() for c in cleaned_text):
                logger.debug(f"OCR extracted {len(cleaned_text)} characters from image in slide {slide_number}")
                return cleaned_text.strip()

        return None

    except Exception as e:
        logger.warning(f"OCR extraction failed for image in slide {slide_number}: {e}")
        return None


def extract_text_from_full_page_image(shape, slide_number):
    """Extract text from full-page image shapes using enhanced OCR and AI vision as fallback."""
    if not OCR_AVAILABLE:
        return None

    try:
        # Extract image data from shape
        image_data = None
        
        # Method 1: Try to get image from shape.image
        if hasattr(shape, 'image') and shape.image is not None:
            try:
                image_data = shape.image.blob
                logger.debug(f"Extracted image data using shape.image method for slide {slide_number}")
            except Exception as e:
                logger.debug(f"Failed to get image blob from shape.image: {e}")

        # Method 2: Try alternative image extraction methods
        if not image_data:
            try:
                if hasattr(shape, '_element') and hasattr(shape._element, 'xpath'):
                    # Look for embedded images in the shape XML
                    blip_elements = shape._element.xpath('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'})
                    if blip_elements:
                        embed_id = blip_elements[0].get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed_id and hasattr(shape, 'part') and hasattr(shape.part, 'related_parts'):
                            related_part = shape.part.related_parts.get(embed_id)
                            if related_part and hasattr(related_part, 'blob'):
                                image_data = related_part.blob
                                logger.debug(f"Extracted image data from related part for slide {slide_number}")
            except Exception as e:
                logger.debug(f"Alternative image extraction failed: {e}")

        if not image_data:
            logger.warning(f"No image data found for full-page image on slide {slide_number}")
            return None

        # Convert to PIL Image
        import io
        image_stream = io.BytesIO(image_data)
        pil_image = PILImage.open(image_stream)

        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        logger.info(f"Processing full-page image ({pil_image.size[0]}x{pil_image.size[1]}) on slide {slide_number}")

        # Enhanced preprocessing for full-page images
        import numpy as np
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # Multiple OCR approaches for better results
        extracted_texts = []

        # Approach 1: High-quality OCR with minimal preprocessing
        try:
            # Convert to grayscale for better OCR
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply slight Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (1, 1), 0)
            
            # Use high-quality tesseract settings for document images
            custom_config = r'--oem 3 --psm 3 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` "'
            
            processed_pil = PILImage.fromarray(blurred)
            text_approach1 = pytesseract.image_to_string(processed_pil, config=custom_config)
            
            if text_approach1 and len(text_approach1.strip()) > 10:
                extracted_texts.append(("minimal_processing", text_approach1.strip()))
                logger.debug(f"Approach 1 (minimal) extracted {len(text_approach1)} characters")
        except Exception as e:
            logger.debug(f"OCR Approach 1 failed: {e}")

        # Approach 2: Enhanced preprocessing for difficult images
        try:
            gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray, h=10)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 2
            )
            
            # Apply morphological operations to clean up
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            processed_pil = PILImage.fromarray(processed)
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` "'
            
            text_approach2 = pytesseract.image_to_string(processed_pil, config=custom_config)
            
            if text_approach2 and len(text_approach2.strip()) > 10:
                extracted_texts.append(("enhanced_processing", text_approach2.strip()))
                logger.debug(f"Approach 2 (enhanced) extracted {len(text_approach2)} characters")
        except Exception as e:
            logger.debug(f"OCR Approach 2 failed: {e}")

        # Approach 3: Multi-scale OCR for different text sizes
        try:
            original_height, original_width = opencv_image.shape[:2]
            
            # Try different scaling factors
            scale_factors = [1.5, 2.0, 0.8]  # Scaling factors for different text sizes
            
            for scale in scale_factors:
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)
                
                # Resize image
                resized = cv2.resize(opencv_image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                gray_resized = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
                
                # Apply basic processing
                processed_pil = PILImage.fromarray(gray_resized)
                custom_config = r'--oem 3 --psm 6'
                
                text_scaled = pytesseract.image_to_string(processed_pil, config=custom_config)
                
                if text_scaled and len(text_scaled.strip()) > 10:
                    extracted_texts.append((f"scale_{scale}", text_scaled.strip()))
                    logger.debug(f"Scale {scale} extracted {len(text_scaled)} characters")
                    break  # Use first successful scale
        except Exception as e:
            logger.debug(f"Multi-scale OCR failed: {e}")

        # Choose the best extraction result
        best_text = None
        if extracted_texts:
            # Prefer the longest meaningful text
            best_extraction = max(extracted_texts, key=lambda x: len(x[1]))
            best_text = best_extraction[1]
            logger.info(f"Best OCR result from {best_extraction[0]} method: {len(best_text)} characters")

        # Fallback: Use OpenAI Vision API for complex images if OCR fails
        if not best_text or len(best_text.strip()) < 20:
            try:
                logger.info(f"OCR extraction insufficient for slide {slide_number}, trying AI vision...")
                ai_text = extract_text_with_ai_vision(image_data, f"slide_{slide_number}")
                if ai_text and len(ai_text.strip()) > len(best_text or ""):
                    best_text = ai_text
                    logger.info(f"AI vision provided better result: {len(ai_text)} characters")
            except Exception as e:
                logger.warning(f"AI vision fallback failed: {e}")

        # Clean and return the best text
        if best_text:
            # Clean up the text
            cleaned_text = ' '.join(best_text.split())
            
            # Remove common OCR artifacts
            cleaned_text = cleaned_text.replace('|', 'I')  # Common OCR mistake
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)  # Normalize whitespace
            
            return cleaned_text.strip()

        return None

    except Exception as e:
        logger.error(f"Full-page OCR extraction failed for slide {slide_number}: {e}")
        return None

def extract_text_with_ai_vision(image_data, identifier):
    """Extract text from image using OpenAI's vision model as fallback for OCR."""
    try:
        # Encode image for API
        base64_image = base64.b64encode(image_data).decode('utf-8')

        # Specialized prompt for insurance document images
        vision_prompt = """
Extract ALL visible text from this image with maximum accuracy. This appears to be a page from an insurance or business document.

Focus on:
1. All text content including headers, body text, tables, and fine print
2. Policy numbers, dates, amounts, and numerical data
3. Names, addresses, and contact information
4. Terms, conditions, and legal text
5. Any formatted content like tables or lists

Preserve the logical structure and organization of the text. Return only the extracted text content without commentary.
"""

        # Use OpenAI's vision model
        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Vision-capable model
            messages=[
                {
                    "role": "user", 
                    "content": [
                        {
                            "type": "text",
                            "text": vision_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"  # High detail for better text extraction
                            }
                        }
                    ]
                }
            ],
            max_tokens=4000,  # Increased for longer documents
            temperature=0.1   # Very low temperature for accurate extraction
        )

        extracted_text = response.choices[0].message.content
        
        if extracted_text:
            logger.info(f"AI vision extracted {len(extracted_text)} characters from {identifier}")
            return extracted_text.strip()

        return None

    except Exception as e:
        logger.error(f"AI vision extraction failed for {identifier}: {e}")
        return None


def extract_text_from_slide_aggressively(slide, slide_number):
    """Aggressively extract text from slides with no regular text content using OCR."""
    if not OCR_AVAILABLE:
        return []

    extracted_texts = []

    try:
        # First, try to identify if this is a full-page image slide
        full_page_image_detected = False
        largest_shape = None
        largest_area = 0

        # Find the largest shape (likely the full-page image)
        for shape in slide.shapes:
            if hasattr(shape, 'width') and hasattr(shape, 'height'):
                area = shape.width * shape.height
                if area > largest_area:
                    largest_area = area
                    largest_shape = shape

        # Enhanced detection for full-page images
        if largest_shape and hasattr(slide, 'slide_layout'):
            try:
                # Get slide dimensions more accurately
                slide_width = getattr(slide, 'slide_width', 9144000)  # Default PowerPoint width in EMUs
                slide_height = getattr(slide, 'slide_height', 6858000)  # Default PowerPoint height in EMUs
                slide_area = slide_width * slide_height
                coverage_ratio = largest_area / slide_area

                # More aggressive detection for image slides
                if coverage_ratio > 0.4:  # Lowered threshold to 40%
                    full_page_image_detected = True
                    logger.info(f"Full-page image detected on slide {slide_number} (coverage: {coverage_ratio:.1%})")
            except Exception as e:
                # Fallback: use absolute size threshold
                if largest_area > 30000000:  # Lowered threshold
                    full_page_image_detected = True
                    logger.info(f"Large image detected on slide {slide_number} (area: {largest_area})")

        # Process full-page image with enhanced OCR
        if full_page_image_detected and largest_shape:
            try:
                logger.info(f"Processing full-page image on slide {slide_number} with enhanced OCR...")
                ocr_text = extract_text_from_full_page_image(largest_shape, slide_number)
                if ocr_text and len(ocr_text.strip()) > 5:
                    extracted_texts.append(f"[FULL_PAGE_IMAGE_TEXT] {ocr_text}")
                    logger.info(f"Full-page OCR extracted {len(ocr_text)} characters from slide {slide_number}")
                    return extracted_texts  # Return early if successful
            except Exception as e:
                logger.warning(f"Full-page OCR failed for slide {slide_number}: {e}")

        # Fallback: Try OCR on all shapes that might contain images
        for shape in slide.shapes:
            try:
                # Skip shapes that already have text
                if hasattr(shape, 'text') and shape.text.strip():
                    continue

                # Try OCR on any shape that might be an image
                if hasattr(shape, 'shape_type'):
                    # Expanded list of potential image/media shape types
                    potential_image_types = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]

                    if shape.shape_type in potential_image_types:
                        # Try to extract text using OCR
                        ocr_text = extract_text_from_image_shape(shape, slide_number)
                        if ocr_text and len(ocr_text.strip()) > 2:
                            extracted_texts.append(f"[IMAGE_TEXT] {ocr_text}")
                            logger.info(f"Shape-level OCR found text in slide {slide_number}: {ocr_text[:100]}...")

                # Also try on shapes with specific dimensions that might be images
                if (hasattr(shape, 'width') and hasattr(shape, 'height') and
                    shape.width > 100 and shape.height > 100):

                    # Skip if we already processed this shape
                    if any(f"[IMAGE_TEXT]" in text for text in extracted_texts):
                        continue

                    ocr_text = extract_text_from_image_shape(shape, slide_number)
                    if ocr_text and len(ocr_text.strip()) > 2:
                        extracted_texts.append(f"[IMAGE_TEXT] {ocr_text}")
                        logger.info(f"Dimensional OCR found text in large shape on slide {slide_number}: {ocr_text[:100]}...")

            except Exception as e:
                logger.debug(f"Shape-level OCR failed for shape in slide {slide_number}: {e}")
                continue

        # If still no text found, try alternative approaches
        if not extracted_texts:
            try:
                logger.debug(f"No text found with shape-level OCR on slide {slide_number}")

                # Try to find any embedded images in the slide XML
                if hasattr(slide, '_element'):
                    # Look for any image references in the slide
                    import xml.etree.ElementTree as ET
                    slide_xml = ET.tostring(slide._element, encoding='unicode')

                    # Check if there are image references
                    if 'blip' in slide_xml or 'pic' in slide_xml or 'image' in slide_xml.lower():
                        logger.info(f"Slide {slide_number} contains image references but OCR extraction failed")
                        # Add a placeholder to indicate there might be image content
                        extracted_texts.append("[IMAGE_TEXT] [Image detected but text extraction failed]")

            except Exception as e:
                logger.debug(f"Slide XML analysis failed for slide {slide_number}: {e}")

        return extracted_texts

    except Exception as e:
        logger.warning(f"Aggressive OCR processing failed for slide {slide_number}: {e}")
        return []


def extract_text_from_image_file(image_path):
    """Extract text from image file using OCR with enhanced preprocessing."""
    if not OCR_AVAILABLE:
        return None

    try:
        # Load image
        pil_image = PILImage.open(image_path)

        # Convert to RGB if necessary
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')

        # Apply similar preprocessing as shape extraction
        import numpy as np
        opencv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        gray = cv2.cvtColor(opencv_image, cv2.COLOR_BGR2GRAY)
        denoised = cv2.fastNlMeansDenoising(gray)
        thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

        # Convert back to PIL
        processed_pil = PILImage.fromarray(thresh)

        # Extract text
        custom_config = r'--oem 3 --psm 6'
        extracted_text = pytesseract.image_to_string(processed_pil, config=custom_config)

        if extracted_text:
            cleaned_text = ' '.join(extracted_text.split())
            if len(cleaned_text) >= 3:
                return cleaned_text.strip()

        return None

    except Exception as e:
        logger.warning(f"OCR extraction failed for image file {image_path}: {e}")
        return None


def is_sensitive_data_query(question):
    """Determine if a question is asking for sensitive or confidential information.

    Uses GPT-4o mini to analyze if the question is requesting sensitive data that
    should not be shared for privacy/security reasons.

    Args:
        question: The user's question text

    Returns:
        bool: True if the question is asking for sensitive information
    """
    try:
        # Create a focused prompt for sensitive data classification
        sensitive_prompt = f"""You are a data privacy expert that identifies questions asking for sensitive or confidential information.

Analyze this question and determine if it's requesting sensitive data:

Question: "{question}"

SENSITIVE data includes:
- Personal identifiers (SSN, passport numbers, driver's license)
- Financial account numbers, credit card numbers, bank details
- Personal addresses, phone numbers, email addresses
- Medical records, health information, diagnoses
- Login credentials, passwords, security codes
- Employee IDs, salary information, HR records
- Legal case details, court records
- Private communications, emails, messages
- Confidential business information, trade secrets
- Any personally identifiable information (PII)

NON-SENSITIVE data includes:
- General statistics, aggregated data, trends
- Public information, general knowledge
- Policy information, coverage details, general terms
- Process explanations, how-to information
- General business metrics (without personal details)
- Educational content, definitions
- General insurance information

Respond with ONLY one word:
- "SENSITIVE" if the question asks for sensitive/confidential data
- "NON-SENSITIVE" if the question asks for general/public information

Classification:"""

        # Use GPT-4o mini for classification
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                    "role": "user",
                    "content": sensitive_prompt
                }
            ],
            max_tokens=10,  # We only need one word response
            temperature=0.1,  # Low temperature for consistent classification
            timeout=10  # Quick timeout for fast classification
        )

        classification = response.choices[0].message.content.strip().upper()

        # Log the classification for debugging
        logger.info(f"Sensitive data check for question: '{question[:50]}...' -> Classification: {classification}")

        # Return True if classified as sensitive
        return classification == "SENSITIVE"

    except Exception as e:
        logger.error(f"Error in sensitive data classification for question '{question[:50]}...': {e}")

        # Fallback to keyword-based sensitive data detection
        logger.info("Falling back to keyword-based sensitive data detection")
        return fallback_sensitive_data_check(question)


def fallback_sensitive_data_check(question):
    """Fallback keyword-based sensitive data detection when API fails.

    Args:
        question: The user's question text

    Returns:
        bool: True if the question appears to ask for sensitive information
    """
    question_lower = question.lower()

    # Keywords that typically indicate sensitive data requests
    sensitive_keywords = [
        'ssn', 'social security', 'passport', 'driver license', 'credit card',
        'bank account', 'account number', 'password', 'login', 'email address',
        'phone number', 'address', 'salary', 'employee id', 'medical record',
        'diagnosis', 'health record', 'personal information', 'confidential',
        'private', 'secret', 'security code', 'pin number'
    ]

    # Check for sensitive keywords
    for keyword in sensitive_keywords:
        if keyword in question_lower:
            return True

    # Check for patterns that might indicate requests for personal data
    sensitive_patterns = [
        r'(?i)(show|give|provide|list|display)\s+(me\s+)?(all\s+)?(personal|private|confidential)',
        r'(?i)(what|who)\s+(is|are)\s+(my|the|their)\s+(address|phone|email|ssn)',
        r'(?i)(account|card|license)\s+number',
        r'(?i)(personal|private|confidential)\s+(data|information|details)'
    ]

    for pattern in sensitive_patterns:
        if re.search(pattern, question):
            return True

    return False


def generate_sensitive_data_response():
    """Generate a response for sensitive data requests.

    Returns:
        str: A message explaining that sensitive data cannot be shared
    """
    return ("I cannot provide sensitive or confidential information such as personal identifiers, "
            "account numbers, addresses, phone numbers, medical records, or other private data. "
            "This is to protect privacy and maintain data security. If you need access to your "
            "prsonal information, please contact the appropriate organization directly or use "
            "their secure portal.")


def process_excel_csv_question_with_checks(question, data_result):
    """Process a question for Excel/CSV data with sensitive data and domain checks.

    Args:
        question: The user's question
        data_result: The processed Excel/CSV data

    Returns:
        str: The appropriate response based on the checks
    """
    # First check: Is this asking for sensitive data?
    if is_sensitive_data_query(question):
        logger.info(f"Sensitive data query detected for Excel/CSV: {question[:100]}...")
        return generate_sensitive_data_response()

    # Second check: Is this domain-specific (insurance-related)?
    if not is_insurance_related_query(question):
        logger.info(f"Non-insurance query detected for Excel/CSV: {question[:100]}...")
        return generate_non_insurance_response(question)

    # If it passes both checks, process the question normally
    return answer_question_from_data(question, data_result)


def generate_non_insurance_response(question):
    """Generate a friendly response for non-insurance related queries
    
    Args:
        question: The user's original question
        
    Returns:
        str: A friendly response that guides the user back to insurance topics
    """
    responses = [
        "I'm specialized in answering questions about insurance policies and coverage. Would you like to ask something about your insurance instead?",
        
        "I'm an insurance assistant trained to help with policy questions. While I'd love to help with this query, it appears to be outside my insurance expertise. Is there something insurance-related I can assist you with?",
        
        "I'm here to help you understand insurance policies and coverage. This question seems to be about something else. Could you please ask me something about insurance or your policy?",
        
        "My expertise is in insurance matters - policies, claims, coverage and related topics. For this question, you might want to try a general assistant instead. However, I'm ready to help with any insurance questions you may have.",
        
        "I'm designed to provide information specifically about insurance policies and coverage. This appears to be outside my area of expertise. Is there an insurance-related question I can help with instead?"
    ]
    
    # Choose a response based on question hash to ensure consistent responses
    # for the same question but varying responses for different questions
    response_index = hash(question) % len(responses)
    return responses[response_index]

def call_gpt_fast(prompt, question=""):
    """Make LLM call with adaptive parameters based on question type
    
    Args:
        prompt: The full prompt to send to the LLM
        question: The user's original question (used for type detection)
    
    Returns:
        str: The formatted response from the LLM
    """
    try:
        # Check if the question is insurance/policy related
        if question and not is_insurance_related_query(question):
            logger.info(f"Non-insurance question detected: {question}")
            return generate_non_insurance_response(question)
        
        # Determine if question is factual or explanatory
        question_type = determine_question_type(question) if question else "factual"
        
        # Set parameters based on question type
        if question_type == "factual":
            # Lower temperature for factual questions (more precise)
            temperature = 0.05
            top_p = 0.1
        else:  # explanatory
            # Higher temperature for explanatory questions (more fluent)
            temperature = 0.3
            top_p = 0.2
        
        response = client.chat.completions.create(
            model="gpt-4.1-mini",  # Faster and more cost-effective
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,  # Adaptive based on question type
            max_tokens=600,  # Reduced tokens for faster response
            top_p=top_p,  # Adaptive based on question type
            response_format={"type": "json_object"}  # Force JSON output format
        )
        content = response.choices[0].message.content

        try:
            # Parse the JSON response
            parsed_json = json.loads(content)
            answer = parsed_json.get("answer")
            if answer and answer.strip():
                # Format the response to handle newlines properly
                return format_response_text(answer)
        except json.JSONDecodeError:
            # Optimized fallback extraction
            if '{"answer":' in content:
                try:
                    start = content.find('{"answer":')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    answer = json.loads(json_str).get("answer", "Not found in document.")
                    return format_response_text(answer)
                except:
                    pass

            # Last resort: regex extraction
            answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', content)
            if answer_match:
                return format_response_text(answer_match.group(1))

        return "Not found in document."
    except Exception as e:
        return f"Error: {str(e)}"


@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
def run_submission():
    # Handle CORS preflight requests
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers
    
    try:
        # Debug logging
        print(f"DEBUG: Request headers: {dict(request.headers)}")
        print(f"DEBUG: Request content type: {request.content_type}")
        print(f"DEBUG: Request data: {request.data}")
        
        auth_header = request.headers.get("Authorization", "")
        print(f"DEBUG: Auth header: {auth_header}")
        
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            print(f"DEBUG: Authorization failed. Expected: Bearer {TEAM_TOKEN}")
            return jsonify({"error": "Unauthorized"}), 401
        
        # Always use an extremely lenient JSON parser
        try:
            # First attempt: Use Flask's built-in parser with force=True
            try:
                data = request.get_json(force=True)
                if data:
                    pass
            except:
                # Second attempt: Try to parse raw data directly
                try:
                    # Try to decode with different encodings
                    try:
                        raw_data = request.data.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            raw_data = request.data.decode('latin-1')
                        except:
                            raw_data = request.data.decode('utf-8', errors='replace')
                    
                    # Extract the URL and questions using regex (most reliable for malformed JSON)
                    
                    # Look for URL in the document field (handles both quoted and unquoted)
                    # More comprehensive URL pattern that handles query params with special characters
                    url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
                    document_match = re.search(url_pattern, raw_data, re.IGNORECASE)
                    
                    # Look for questions array
                    questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
                    questions_match = re.search(questions_pattern, raw_data, re.DOTALL)
                    
                    if document_match and questions_match:
                        # Extract document URL - group(1) contains the full URL match
                        document_url = document_match.group(1).split('"')[0].split('\\')[0]
                        # Extract and clean up questions
                        questions_text = questions_match.group(1)
                        questions = []
                        # Extract individual questions with quotes (handles both single and double quotes)
                        for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                            if match.group(1):  # Double quotes match
                                questions.append(match.group(1))
                            else:  # Single quotes match
                                questions.append(match.group(2))
                                
                        data = {"documents": document_url, "questions": questions}
                    else:
                        # If regex fails, try standard JSON parsing with cleaning
                        cleaned = raw_data.strip()
                        # Fix common JSON formatting issues
                        cleaned = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned)
                        data = json.loads(cleaned)
                
                except Exception as inner_e:
                    error_msg = f"All parsing attempts failed: {str(inner_e)}"
                    return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
                    
        except Exception as e:
            error_msg = f"JSON parsing error: {str(e)}"
            return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
            
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        # COMPREHENSIVE QUESTION CLASSIFICATION SYSTEM
        # Step 1: SENSITIVE DATA CHECK - First priority to protect privacy
        # Step 2: DOMAIN CHECK - Second priority to ensure insurance relevance
        # Step 3: PROCESSING - Only for questions that pass both checks

        logger.info(f"Starting comprehensive classification for {len(questions)} questions")

        # Classification tracking
        question_classification = {}  # Track classification results for each question
        sensitive_questions = []
        non_domain_questions = []
        valid_questions = []  # Questions that pass both sensitive and domain checks

        # Step 1: Sensitive Data Check (First Priority)
        logger.info("Step 1: Performing sensitive data classification...")
        for i, question in enumerate(questions):
            if is_sensitive_data_query(question):
                question_classification[i] = "sensitive"
                sensitive_questions.append(question)
                logger.info(f"SENSITIVE data query detected: {question[:100]}...")
            else:
                question_classification[i] = "non_sensitive"
                logger.info(f"Non-sensitive query: {question[:100]}...")

        # Step 2: Domain Check (Second Priority) - Only for non-sensitive questions
        logger.info("Step 2: Performing domain classification for non-sensitive questions...")
        for i, question in enumerate(questions):
            if question_classification[i] == "non_sensitive":  # Only check non-sensitive questions
                if is_insurance_related_query(question):
                    question_classification[i] = "valid"  # Passes both checks
                    valid_questions.append(question)
                    logger.info(f"VALID insurance query: {question[:100]}...")
                else:
                    question_classification[i] = "non_domain"
                    non_domain_questions.append(question)
                    logger.info(f"Non-insurance query: {question[:100]}...")

        # Log classification summary
        logger.info(f"Classification Summary: {len(sensitive_questions)} sensitive, {len(non_domain_questions)} non-domain, {len(valid_questions)} valid")

        # If NO questions are valid (all are either sensitive or non-domain), return immediately
        if len(valid_questions) == 0:
            logger.info("No valid questions found. Returning classification-based responses without document processing.")

            # Build responses based on classification
            final_answers = []
            for i, question in enumerate(questions):
                if question_classification[i] == "sensitive":
                    final_answers.append(generate_sensitive_data_response())
                else:  # non_domain
                    final_answers.append(generate_non_insurance_response(question))

            response_data = {"answers": final_answers}
            response = jsonify(response_data)
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            return response, 200

        # If we have valid questions, proceed with document processing
        logger.info(f"Proceeding with document processing for {len(valid_questions)} valid questions out of {len(questions)} total questions.")

        
        # Check if this is a ZIP file and handle it efficiently without LLM
        if is_zip_url(document_url):
            try:
                # Only process ZIP file if we have valid questions
                if len(valid_questions) > 0:
                    zip_result = process_zip_from_url(document_url)
                    if zip_result.get("success", False):
                        # Enhanced ZIP response handling
                        is_zip_trap = zip_result.get("is_zip_trap", False)
                        total_nested_zips = zip_result.get("total_nested_zips", 0)
                        total_files = zip_result.get("total_files", 0)

                        # Create a clean, direct response format
                        def create_clean_zip_response():
                            filename = zip_result.get("filename", "archive.zip")

                            if is_zip_trap:
                                # For nested ZIP files, create a clean summary
                                zip_files = zip_result.get("zip_files", [])
                                size_kb = zip_result.get("size_kb", 0)

                                response = f"The ZIP file '{filename}' contains {total_nested_zips} nested ZIP files.\\n\\n"

                                if total_nested_zips > 1:
                                    response += "This appears to be an archive of archives with a nested structure.\\n\\n"
                                else:
                                    response += "This appears to be an archive containing another archive.\\n\\n"

                                if zip_files:
                                    response += "Nested archives include:\\n"
                                    # Show first 10 files
                                    for zip_name in zip_files[:10]:
                                        response += f"- {zip_name}\\n"
                                    if len(zip_files) > 10:
                                        response += f"... and {len(zip_files) - 10} more\\n"
                                    response += "\\n"

                                response += f"Total size: {size_kb:.2f} KB\\n\\n"
                                response += "To view the contents of these nested archives, you would need to extract them individually."

                                return response
                            else:
                                # For regular ZIP files, create a clean summary
                                file_types = zip_result.get("file_types", {})
                                size_kb = zip_result.get("size_kb", 0)

                                response = f"The ZIP file '{filename}' contains {total_files:,} files.\\n\\n"
                                response += f"Total size: {size_kb:.2f} KB\\n\\n"

                                if file_types:
                                    response += "File types include:\\n"
                                    for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                                        ext_display = ext if ext != 'no_extension' else '(no extension)'
                                        response += f"- {ext_display}: {count} file{'s' if count != 1 else ''}\\n"
                                    if len(file_types) > 5:
                                        response += f"... and {len(file_types) - 5} more file types\\n"
                                    response += "\\n"

                                response += "You can extract this archive to access all the files."
                                return response

                        # Generate the clean response for domain-specific questions only
                        clean_response = create_clean_zip_response()

                        # Build final answers array matching the original question order using comprehensive classification
                        final_answers = []

                        for i, question in enumerate(questions):
                            if question_classification[i] == "sensitive":
                                final_answers.append(generate_sensitive_data_response())
                            elif question_classification[i] == "non_domain":
                                final_answers.append(generate_non_insurance_response(question))
                            else:  # valid question
                                final_answers.append(format_response_text(clean_response))

                        # Create response with proper JSON formatting that preserves newlines
                        response_data = {"answers": final_answers}
                        response = jsonify(response_data)
                        response.headers['Content-Type'] = 'application/json; charset=utf-8'
                        return response, 200
                    else:
                        # Enhanced error handling with user-friendly messages
                        error_msg = zip_result.get("user_friendly_error", zip_result.get("error", "Unknown error processing ZIP file"))
                        detailed_summary = zip_result.get("summary", f"Error processing ZIP file: {error_msg}")

                        # Return both the error and the detailed summary for better user experience
                        return jsonify({
                            "error": f"ZIP processing error: {error_msg}",
                            "detailed_error": detailed_summary,
                            "error_type": zip_result.get("error", "unknown"),
                            "suggestions": "Please check the detailed error information for troubleshooting steps."
                        }), 400
                else:
                    # This case should have been handled earlier, but just in case
                    logger.warning("ZIP processing reached with no domain-specific questions - this should have been handled earlier")
                    single_message = "I'm specialized in answering questions about insurance policies and coverage. The questions you've asked appear to be outside my insurance expertise. Please ask me questions related to insurance policies, claims, coverage, premiums, or other insurance-related topics."
                    response_data = {"answers": [single_message] * len(questions)}
                    response = jsonify(response_data)
                    response.headers['Content-Type'] = 'application/json; charset=utf-8'
                    return response, 200
            except Exception as e:
                return jsonify({"error": f"Error processing ZIP file: {str(e)}"}), 400

        # Check if this is an Excel/CSV file and handle it with sensitive data and domain checks
        if is_excel_or_csv_url(document_url):
            try:
                data_result = process_excel_csv_from_url(document_url)
                if data_result.get("success", False):
                    # Process ALL questions for Excel/CSV files with sensitive data and domain checks
                    final_answers = []
                    for question in questions:
                        # Use the new function that handles both sensitive data and domain checks
                        answer = process_excel_csv_question_with_checks(question, data_result)
                        formatted_answer = format_response_text(answer)
                        final_answers.append(formatted_answer)

                    answers = final_answers
                    # Create response with proper JSON formatting that preserves newlines
                    response_data = {"answers": answers}
                    response = jsonify(response_data)
                    response.headers['Content-Type'] = 'application/json; charset=utf-8'
                    return response, 200
                else:
                    error_msg = data_result.get("user_friendly_error", data_result.get("error", "Unknown error"))
                    return jsonify({"error": f"Data processing error: {error_msg}"}), 400
            except Exception as e:
                return jsonify({"error": f"Error processing Excel/CSV file: {str(e)}"}), 400

        # For all other file types (including images and PowerPoint), use the standard LLM pipeline
        cache_key = get_cache_key(document_url)
        cached = load_cache(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            try:
                text_by_page = extract_text_from_url(document_url)
                chunk_dicts = generate_smart_chunks(text_by_page)

                if not chunk_dicts:
                    return jsonify({"error": "No valid content extracted from document"}), 400

                index, chunks, _ = embed_chunks_openai(chunk_dicts)
                save_cache(cache_key, {"index": index, "chunks": chunks})
            except Exception as e:
                return jsonify({"error": f"Error processing document: {str(e)}"}), 400

        def process_domain_question(q):
            try:
                # Process only insurance-related questions
                top_chunks = insurance_specific_retrieve(q, index, chunks)
                # Validate and filter context for better accuracy
                relevant_chunks = validate_context_relevance(q, top_chunks)
                prompt = build_insurance_prompt(q, relevant_chunks)
                # Pass the original question to enable adaptive temperature
                return call_gpt_fast(prompt, question=q)
            except Exception as e:
                return f"Error processing question: {str(e)}"

        # Process only valid questions (those that passed both sensitive and domain checks)
        # Optimize thread pool size based on number of valid questions
        max_workers = min(len(valid_questions), 5)  # Cap at 5 to avoid overwhelming the API
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            valid_answers = list(executor.map(process_domain_question, valid_questions))

        # Build comprehensive final answers array matching the original question order
        final_answers = []
        valid_answer_index = 0

        for i, question in enumerate(questions):
            if question_classification[i] == "sensitive":
                final_answers.append(generate_sensitive_data_response())
            elif question_classification[i] == "non_domain":
                final_answers.append(generate_non_insurance_response(question))
            else:  # valid question
                final_answers.append(valid_answers[valid_answer_index])
                valid_answer_index += 1

        answers = final_answers

        # Log final response summary
        logger.info(f"Returning comprehensive response: {len([a for i, a in enumerate(final_answers) if question_classification[i] == 'valid'])} processed, {len([a for i, a in enumerate(final_answers) if question_classification[i] == 'sensitive'])} sensitive blocked, {len([a for i, a in enumerate(final_answers) if question_classification[i] == 'non_domain'])} non-domain blocked")

        # Create response with proper JSON formatting that preserves newlines
        response_data = {"answers": answers}

        # Use Flask's jsonify but ensure newlines are preserved
        response = jsonify(response_data)
        response.headers['Content-Type'] = 'application/json; charset=utf-8'

        return response, 200

    except Exception as e:
        error_msg = f"Server error: {str(e)}"
        return jsonify({"error": "Server error", "details": error_msg}), 500


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy"}), 200


@app.route("/debug/json", methods=["POST"])
def debug_json():
    """Debug endpoint to check JSON parsing"""
    try:
        # Raw data
        raw_data = request.data.decode('utf-8')
        
        # Try all parsing methods
        parsed = {}
        
        # Method 1: Standard JSON parsing
        try:
            parsed["standard"] = json.loads(raw_data)
        except Exception as e:
            parsed["standard_error"] = str(e)
        
        # Method 2: Flask's built-in parser
        try:
            parsed["flask"] = request.get_json(force=True)
        except Exception as e:
            parsed["flask_error"] = str(e)
        
        # Method 3: Cleaned regex approach
        try:
            cleaned_data = raw_data.strip()
            cleaned_data = re.sub(r'"\s*:\s*"', '":"', cleaned_data)
            cleaned_data = re.sub(r'"\s*,\s*"', '","', cleaned_data)
            cleaned_data = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned_data)
            parsed["cleaned"] = json.loads(cleaned_data)
        except Exception as e:
            parsed["cleaned_error"] = str(e)
            parsed["cleaned_data"] = cleaned_data
        
        # Return all results
        return jsonify({
            "raw_data": raw_data,
            "parsing_results": parsed,
            "content_type": request.content_type,
            "is_json": request.is_json
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/test/ppt", methods=["POST"])
def test_powerpoint_processing():
    """Test endpoint for PowerPoint processing with enhanced image support"""
    try:
        data = request.get_json()
        
        if not data or 'url' not in data:
            return jsonify({"error": "Please provide a 'url' field with the PowerPoint file URL"}), 400
        
        ppt_url = data['url']
        
        # Check if it's a PowerPoint file
        if not is_powerpoint_file(ppt_url):
            return jsonify({"error": "URL does not appear to be a PowerPoint file"}), 400
        
        logger.info(f"Testing PowerPoint processing for: {ppt_url}")
        
        # Download and process the PowerPoint file
        try:
            response = requests.get(ppt_url, timeout=30)
            response.raise_for_status()
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_file_path = temp_file.name
            
            try:
                # Process with enhanced PowerPoint function
                extracted_content = process_powerpoint_for_qa(temp_file_path, "test_presentation.pptx")
                
                # Analyze the results
                result_analysis = {
                    "file_url": ppt_url,
                    "total_content_chunks": len(extracted_content) if extracted_content else 0,
                    "processing_status": "success",
                    "content_summary": {},
                    "sample_content": []
                }
                
                if extracted_content:
                    # Analyze content types
                    content_types = {
                        "regular_text": 0,
                        "table_content": 0,
                        "chart_content": 0,
                        "image_text": 0,
                        "full_page_images": 0,
                        "image_only_slides": 0
                    }
                    
                    total_length = 0
                    for chunk in extracted_content:
                        total_length += len(chunk)
                        
                        if "[FULL_PAGE_IMAGE_TEXT]" in chunk:
                            content_types["full_page_images"] += 1
                        elif "[IMAGE_TEXT]" in chunk:
                            content_types["image_text"] += 1
                        elif "[TABLE]" in chunk:
                            content_types["table_content"] += 1
                        elif "[CHART]" in chunk:
                            content_types["chart_content"] += 1
                        elif "[IMAGE_SLIDE]" in chunk or "[NO_CONTENT]" in chunk:
                            content_types["image_only_slides"] += 1
                        else:
                            content_types["regular_text"] += 1
                    
                    result_analysis["content_summary"] = {
                        "total_characters": total_length,
                        "average_chunk_size": total_length // len(extracted_content),
                        "content_types": content_types,
                        "image_processing_success": content_types["full_page_images"] + content_types["image_text"] > 0
                    }
                    
                    # Provide sample content (first 3 chunks, truncated)
                    for i, chunk in enumerate(extracted_content[:3]):
                        sample = chunk[:500] + "..." if len(chunk) > 500 else chunk
                        result_analysis["sample_content"].append({
                            "chunk_index": i,
                            "content_type": "full_page_image" if "[FULL_PAGE_IMAGE_TEXT]" in chunk else 
                                           "image_text" if "[IMAGE_TEXT]" in chunk else
                                           "table" if "[TABLE]" in chunk else
                                           "chart" if "[CHART]" in chunk else
                                           "image_only" if "[IMAGE_SLIDE]" in chunk or "[NO_CONTENT]" in chunk else
                                           "regular_text",
                            "sample_text": sample,
                            "full_length": len(chunk)
                        })
                else:
                    result_analysis["processing_status"] = "no_content_extracted"
                    result_analysis["content_summary"] = {"error": "No content could be extracted from the PowerPoint file"}
                
                return jsonify(result_analysis), 200
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except requests.RequestException as e:
            return jsonify({"error": f"Failed to download PowerPoint file: {str(e)}"}), 400
        except Exception as e:
            return jsonify({"error": f"Failed to process PowerPoint file: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"PowerPoint test error: {e}")
        return jsonify({"error": f"Test failed: {str(e)}"}), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 5000)))